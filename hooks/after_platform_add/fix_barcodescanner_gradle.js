#!/usr/bin/env node

/**
 * Cordova Hook: Fix Barcode Scanner Gradle Configuration
 * 
 * This hook automatically applies a patch to fix the barcode scanner plugin
 * gradle configuration after adding the Android platform.
 * 
 * The patch updates:
 * - jcenter() to mavenCentral() (jcenter is deprecated)
 * - compile() to implementation() (compile is deprecated in newer Gradle versions)
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

module.exports = function(context) {
    console.log('Running barcode scanner gradle fix hook...');
    
    // Only run for Android platform
    if (context.opts.platforms.indexOf('android') === -1) {
        console.log('Android platform not found, skipping barcode scanner fix.');
        return;
    }
    
    const projectRoot = context.opts.projectRoot;
    const targetFile = path.join(projectRoot, 'platforms', 'android', 'phonegap-plugin-barcodescanner', 'agrobalance-barcodescanner.gradle');
    const patchFile = path.join(projectRoot, 'hooks', 'patches', 'agrobalance-barcodescanner.patch');
    
    // Check if target file exists
    if (!fs.existsSync(targetFile)) {
        console.log('Target gradle file not found, skipping patch application.');
        return;
    }
    
    // Check if patch file exists
    if (!fs.existsSync(patchFile)) {
        console.error('Patch file not found at:', patchFile);
        return;
    }
    
    try {
        // Read the current content of the target file
        const currentContent = fs.readFileSync(targetFile, 'utf8');
        
        // Check if the file already contains the fixes (to avoid applying patch multiple times)
        if (currentContent.includes('mavenCentral()') && currentContent.includes('implementation(')) {
            console.log('Barcode scanner gradle file already appears to be fixed, skipping patch.');
            return;
        }
        
        // Apply the patch manually (cross-platform approach)
        console.log('Applying barcode scanner gradle fix...');
        
        // Replace jcenter() with mavenCentral()
        let fixedContent = currentContent.replace(/jcenter\(\)/g, 'mavenCentral()');
        
        // Replace compile( with implementation(
        fixedContent = fixedContent.replace(/compile\(/g, 'implementation(');
        
        // Write the fixed content back to the file
        fs.writeFileSync(targetFile, fixedContent, 'utf8');
        
        console.log('Successfully applied barcode scanner gradle fix!');
        console.log('Fixed:');
        console.log('  - jcenter() → mavenCentral()');
        console.log('  - compile() → implementation()');
        
    } catch (error) {
        console.error('Error applying barcode scanner gradle fix:', error.message);
        
        // Fallback: try to use system patch command if available
        try {
            console.log('Attempting to apply patch using system patch command...');
            const patchCommand = `patch -p1 < "${patchFile}"`;
            execSync(patchCommand, { cwd: projectRoot, stdio: 'inherit' });
            console.log('Successfully applied patch using system patch command!');
        } catch (patchError) {
            console.error('Failed to apply patch using system command:', patchError.message);
            console.error('Please manually apply the fixes to:', targetFile);
            console.error('Changes needed:');
            console.error('  - Replace jcenter() with mavenCentral()');
            console.error('  - Replace compile( with implementation(');
        }
    }
};

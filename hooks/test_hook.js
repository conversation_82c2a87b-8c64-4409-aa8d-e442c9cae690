#!/usr/bin/env node

/**
 * Test script to verify the barcode scanner gradle fix hook
 * 
 * This script simulates the hook execution to test if it works correctly
 * without having to remove/add the Android platform repeatedly.
 */

const fs = require('fs');
const path = require('path');

// Simulate the context object that <PERSON><PERSON><PERSON> passes to hooks
const mockContext = {
    opts: {
        projectRoot: process.cwd(),
        platforms: ['android']
    }
};

// Import and run the hook
const hookFunction = require('./after_platform_add/fix_barcodescanner_gradle.js');

console.log('Testing barcode scanner gradle fix hook...');
console.log('Project root:', mockContext.opts.projectRoot);

// Check if the gradle file exists before running the hook
const targetFile = path.join(mockContext.opts.projectRoot, 'platforms', 'android', 'phonegap-plugin-barcodescanner', 'agrobalance-barcodescanner.gradle');

if (fs.existsSync(targetFile)) {
    console.log('Target gradle file found:', targetFile);
    
    // Read current content
    const content = fs.readFileSync(targetFile, 'utf8');
    console.log('\nCurrent content:');
    console.log(content);
    
    // Check current state
    const hasJCenter = content.includes('jcenter()');
    const hasCompile = content.includes('compile(');
    const hasMavenCentral = content.includes('mavenCentral()');
    const hasImplementation = content.includes('implementation(');
    
    console.log('\nCurrent state:');
    console.log('- Has jcenter():', hasJCenter);
    console.log('- Has compile():', hasCompile);
    console.log('- Has mavenCentral():', hasMavenCentral);
    console.log('- Has implementation():', hasImplementation);
    
    if (hasMavenCentral && hasImplementation) {
        console.log('\n✅ File appears to already be fixed!');
    } else {
        console.log('\n⚠️  File needs fixing.');
    }
} else {
    console.log('❌ Target gradle file not found:', targetFile);
    console.log('Make sure the Android platform is added and the barcode scanner plugin is installed.');
}

// Run the hook
try {
    hookFunction(mockContext);
    console.log('\n✅ Hook execution completed successfully!');
} catch (error) {
    console.error('\n❌ Hook execution failed:', error.message);
}

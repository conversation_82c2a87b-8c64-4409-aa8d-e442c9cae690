#!/usr/bin/env node

/**
 * Cordova Hook: Fix Barcode Scanner Gradle Configuration (Plugin Install)
 * 
 * This hook automatically applies a patch to fix the barcode scanner plugin
 * gradle configuration after installing the barcode scanner plugin.
 * 
 * This is a backup hook that runs after plugin installation to ensure
 * the fix is applied even if the plugin is installed separately.
 */

const fs = require('fs');
const path = require('path');

module.exports = function(context) {
    // Only run for the barcode scanner plugin
    if (context.opts.plugin && context.opts.plugin.id !== 'phonegap-plugin-barcodescanner') {
        return;
    }
    
    console.log('Running barcode scanner gradle fix hook (after plugin install)...');
    
    const projectRoot = context.opts.projectRoot;
    const targetFile = path.join(projectRoot, 'platforms', 'android', 'phonegap-plugin-barcodescanner', 'agrobalance-barcodescanner.gradle');
    
    // Check if target file exists
    if (!fs.existsSync(targetFile)) {
        console.log('Target gradle file not found, skipping patch application.');
        return;
    }
    
    try {
        // Read the current content of the target file
        const currentContent = fs.readFileSync(targetFile, 'utf8');
        
        // Check if the file already contains the fixes
        if (currentContent.includes('mavenCentral()') && currentContent.includes('implementation(')) {
            console.log('Barcode scanner gradle file already appears to be fixed, skipping patch.');
            return;
        }
        
        // Apply the patch manually
        console.log('Applying barcode scanner gradle fix...');
        
        // Replace jcenter() with mavenCentral()
        let fixedContent = currentContent.replace(/jcenter\(\)/g, 'mavenCentral()');
        
        // Replace compile( with implementation(
        fixedContent = fixedContent.replace(/compile\(/g, 'implementation(');
        
        // Write the fixed content back to the file
        fs.writeFileSync(targetFile, fixedContent, 'utf8');
        
        console.log('Successfully applied barcode scanner gradle fix!');
        console.log('Fixed:');
        console.log('  - jcenter() → mavenCentral()');
        console.log('  - compile() → implementation()');
        
    } catch (error) {
        console.error('Error applying barcode scanner gradle fix:', error.message);
        console.error('Please manually apply the fixes to:', targetFile);
        console.error('Changes needed:');
        console.error('  - Replace jcenter() with mavenCentral()');
        console.error('  - Replace compile( with implementation(');
    }
};

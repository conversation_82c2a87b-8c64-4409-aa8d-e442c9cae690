# Cordova Hooks and Patches

This directory contains Cordova hooks and patches that automatically fix known issues with plugins and platform configurations.

## Barcode Scanner Plugin Fix

### Problem
The `phonegap-plugin-barcodescanner` plugin generates a gradle file with deprecated configurations:
- Uses `jcenter()` repository (deprecated and shut down)
- Uses `compile()` dependency declaration (deprecated in newer Gradle versions)

### Solution
An automated patch system that:
1. **Patch File**: `patches/agrobalance-barcodescanner.patch` - Contains the diff to fix the gradle configuration
2. **Hook Script**: `after_platform_add/fix_barcodescanner_gradle.js` - Automatically applies the fix after adding Android platform

### What Gets Fixed
- `jcenter()` → `mavenCentral()`
- `compile(name:'barcodescanner-release-2.1.5', ext:'aar')` → `implementation(name:'barcodescanner-release-2.1.5', ext:'aar')`

### How It Works
1. The hook is registered in `config.xml` and runs after the `after_platform_add` event
2. It checks if the target gradle file exists
3. It verifies the file hasn't already been patched (to avoid duplicate applications)
4. It applies the fixes using string replacement (cross-platform approach)
5. Falls back to system `patch` command if string replacement fails

### Manual Application
If the automatic patch fails, you can manually apply the changes to:
`platforms/android/phonegap-plugin-barcodescanner/agrobalance-barcodescanner.gradle`

Or apply the patch manually using:
```bash
patch -p1 < hooks/patches/agrobalance-barcodescanner.patch
```

### Hook Registration
The hooks are registered in `config.xml`:
```xml
<hook type="after_platform_add" src="hooks/after_platform_add/fix_barcodescanner_gradle.js" />
<hook type="after_plugin_install" src="hooks/after_plugin_install/fix_barcodescanner_gradle.js" />
```

### Testing the Hook
To test the hook system:

1. **Test with platform add/remove:**
   ```bash
   npx cordova platform remove android
   npx cordova platform add android
   ```

2. **Test with plugin reinstall:**
   ```bash
   npx cordova plugin remove phonegap-plugin-barcodescanner
   npx cordova plugin add phonegap-plugin-barcodescanner
   ```

3. **Test the hook directly:**
   ```bash
   node hooks/test_hook.js
   ```

### Files Created
- `hooks/patches/agrobalance-barcodescanner.patch` - The patch file
- `hooks/after_platform_add/fix_barcodescanner_gradle.js` - Hook for platform addition
- `hooks/after_plugin_install/fix_barcodescanner_gradle.js` - Hook for plugin installation
- `hooks/test_hook.js` - Test script for the hook system
- `hooks/README.md` - This documentation

### Cross-Platform Compatibility
The hook script is designed to work on:
- Windows
- macOS
- Linux

It uses Node.js built-in modules and avoids platform-specific commands where possible.

### Troubleshooting
If the hook doesn't run:
1. Ensure hooks are executable: `chmod +x hooks/**/*.js`
2. Check that the hooks are registered in `config.xml`
3. Verify Node.js is available in the PATH
4. Run the test script to debug: `node hooks/test_hook.js`

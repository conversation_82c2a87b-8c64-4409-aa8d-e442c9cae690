<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <meta
            name="viewport"
            content="width=device-width, user-scalable=no, initial-scale=1"
        />
        <meta
            http-equiv="Content-Security-Policy"
            content="default-src *; img-src * data:;
              style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; 
              script-src 'self' 'unsafe-inline' 
              'unsafe-eval' https://dev.virtualearth.net/ https://www.google-analytics.com/ https://maps.google.com https://maps.googleapis.com"
        />
        <title>Agrobalance</title>

        <link rel="stylesheet" href="themes/gs.min.css" />
        <link rel="stylesheet" href="themes/iconfont.css" />
        <link rel="stylesheet" href="themes/font-awesome.css" />

        <link
            rel="stylesheet"
            href="themes/jquery.mobile.icons-1.4.5.min.css"
        />
        <link
            rel="stylesheet"
            href="themes/jquery.mobile.structure-1.4.5.min.css"
        />

        <link rel="stylesheet" href="themes/jquery.mobile.datepicker.css" />
        <link
            rel="stylesheet"
            href="themes/jquery.mobile.datepicker.theme.css"
        />

        <link rel="stylesheet" href="themes/default-skin/default-skin.css" />

        <link rel="stylesheet" href="themes/OpenLayers.css" />
        <link rel="stylesheet" href="themes/slick.css" />

        <link rel="stylesheet" href="themes/custom.css" />

        <link rel="stylesheet" href="themes/bubble.css" />

        <script type="text/javascript" src="cordova.js"></script>

        <script type="text/javascript" src="js/constants.js"></script>

        <script
            type="text/javascript"
            src="https://maps.google.com/maps/api/js?v=3&sensor=false&key=AIzaSyCP7hLKtuQLswTWA8THszoC16ph8so8jbw"
        ></script>

        <script
            type="text/javascript"
            src="js/libs/openlayers/proj4.js"
        ></script>
        <script
            type="text/javascript"
            src="js/libs/openlayers/OpenLayers.js"
        ></script>
        <script
            type="text/javascript"
            src="js/libs/openlayers/DynamicMeasure.js"
        ></script>

        <script
            type="text/javascript"
            src="js/libs/moment/moment-with-locales.js"
        ></script>

        <script type="text/javascript" src="js/libs/gps/gps.js"></script>

        <!-- Google Analytics -->
        <script>
            (function (i, s, o, g, r, a, m) {
                i["GoogleAnalyticsObject"] = r;
                (i[r] =
                    i[r] ||
                    function () {
                        (i[r].q = i[r].q || []).push(arguments);
                    }),
                    (i[r].l = 1 * new Date());
                (a = s.createElement(o)), (m = s.getElementsByTagName(o)[0]);
                a.async = 1;
                a.src = g;
                m.parentNode.insertBefore(a, m);
            })(
                window,
                document,
                "script",
                "https://www.google-analytics.com/analytics.js",
                "ga"
            );
        </script>
        <!-- End Google Analytics -->

        <!-- Load the script "js/main.js" as our entry point -->
        <script data-main="js/main" src="js/libs/require/require.js"></script>
    </head>
    <body>
        <!-- PAGE -->
        <div id="page">
            <img
                id="loader"
                src="themes/images/ajax-loader.gif"
                alt="GeoSCAN"
                style="opacity: 0.2"
            />
            <script>
                document.getElementById("loader").style.margin =
                    (window.innerHeight - 46) / 2 +
                    "px " +
                    (window.innerWidth - 46) / 2 +
                    "px";
            </script>
        </div>
        <!-- end PAGE -->
    </body>
</html>

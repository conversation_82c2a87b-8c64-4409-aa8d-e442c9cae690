define([
    'jquery',
    'backbone',
    'router'
], function ($, Backbone, Router) {

    var SessionModel = Backbone.Model.extend({
        url: '',
        login: function (credentials) {
            var that = this;
            var data = {
                grant_type: 'password',
                client_id: 1,
                client_secret: 'HeswUfret8esTuYe',
            };

            $.extend(data, credentials);

            var login = $.ajax({
                url: Constants.servers[credentials.server].ajax_url + '/oauth/access_token',
                data: data,
                type: 'POST'
            });

            return login;
        }
    });

    return new SessionModel();
});
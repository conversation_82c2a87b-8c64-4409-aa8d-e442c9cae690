define([
    'jquery',
    'backbone'
], function ($, Backbone) {
    var selectControl = function(map, plotsVectorLayer) {
        var selectClick = new OpenLayers.Control.SelectFeature(plotsVectorLayer, {
            clickout: true,
            toggle: true,
            multiple: false,
            hover: false,
            box: false
        }
        );

        map.addControl(selectClick);
        selectClick.handlers.feature.deactivate();
            
        // Handler for SelectFeature control.
        // The select control must NOT be active
        var selectClickHandler = new OpenLayers.Handler.Click(
            selectClick,  // The select control
            {
                click: function(evt) {
                    this.unselectAll();
                    
                    var lonLat = this.map.getLonLatFromPixel(evt.xy);
                    var bounds = new OpenLayers.Bounds(lonLat.lon, lonLat.lat);
                    
                    selectClickHandler.selectBox(bounds);
                },
                dblclick: function(evt) {
                }
            },
            {
                single: true,
                double: true,
                stopDouble: true,
                stopSingle: true,
                delay: 200,
                pixelTolerance: 15,
                dblclickTolerance: 20
            }
        );

        selectClickHandler.unselectAll = function() {
            selectClick.unselectAll();
        };
        
        selectClickHandler.select = function(feature) {
            selectClick.select(feature);
        };
        
        selectClickHandler.selectBox = function(bounds) {
            if (bounds instanceof OpenLayers.Bounds) {
                for(var i=0, len = selectClick.layer.features.length; i<len; ++i) {
                    var feature = selectClick.layer.features[i];
                    // check if the feature is displayed
                    if (!feature.getVisibility()) {
                        continue;
                    }

                    if (this.geometryTypes == null || OpenLayers.Util.indexOf(
                            this.geometryTypes, feature.geometry.CLASS_NAME) > -1) {
                        if (bounds.toGeometry().intersects(feature.geometry)) {
                            var dontSelect = _.some(selectClick.layer.selectedFeatures, function(el) {
                                return el.attributes.id === feature.attributes.id || el.attributes.type === feature.attributes.type
                            });
                            
                            if (!dontSelect) {
                                this.select(feature);
                            }
                        }
                    }
                }
            }
        };

        plotsVectorLayer.events.on({
            'featureselected': function (event) {
                if (localStorage.getItem('page_name') == 'task_plots') {
                    Backbone.trigger('gs:displayPlotData', event.feature.attributes.id);
                }
            },
            'featureunselected': function () {
            }
        });

        return selectClickHandler;
    };
    
    return selectControl;
});

define([
    "underscore",
    "backbone",
    "i18n!nls/localization",
    "collections/PinsCollection",
    "views/map/MapControlSelect",
    "views/map/MapControlMeasure",
    "views/map/MapControlClick",
], function (
    _,
    Backbone,
    Localization,
    PinsCollection,
    MapControlSelect,
    MapControlMeasure
) {
    var Map = function () {
        var user_id = localStorage.getItem("user_id");
        var openedPopupPin;

        var Navigation = new OpenLayers.Control.TouchNavigation({
            defaultDblClick: function (event) {
                return;
            },
        });
        var Zoom = new OpenLayers.Control.Zoom();
        var ArgParser = new OpenLayers.Control.ArgParser();
        var self = this;
        var map = new OpenLayers.Map("map", {
            theme: null,
            projection: new OpenLayers.Projection("EPSG:3857"),
            controls: [Navigation, Zoom, ArgParser],
        });

        map.events.register("zoomend", this, function (e) {
            if (!openedPopupPin) {
                return;
            }

            while (map.popups.length > 0) {
                map.removePopup(map.popups[0]);
            }

            var pin = openedPopupPin;
            openedPopupPin = undefined;
            this.openPopup(pin);
        });

        var authData = JSON.parse(localStorage.getItem("authData"));

        var geoScanLayer = new OpenLayers.Layer.XYZ(
            "geoscan",
            Constants.servers[authData.server].geoscan_map_server +
                "/mapcache/gmaps/geo_scan@g/${z}/${x}/${y}.jpeg",
            {
                numZoomLevels: 19,
            }
        );

        // var bingLayer = new OpenLayers.Layer.Bing({
        //     name: 'bing',
        //     key: Constants.bing_api_key,
        //     type: 'AerialWithLabels',
        //     visibility: false,
        // });
        var bingLayer = new OpenLayers.Layer.XYZ(
            "bing",
            "https://atlas.microsoft.com/map/tile?zoom=${z}&x=${x}&y=${y}&tileSize=256&language=EN&api-version=2.0&subscription-key=C1JxT1M3L84fqOjkOBzy6IxKESmqD5GUH2eog80rUquG0cdSV8dPJQQJ99BGACYeBjFTsUKGAAAgAZMPG8rf&tilesetId=microsoft.imagery",
            {
                numZoomLevels: 19,
            }
        );
        var googleLayer = geoScanLayer;
        var googleLayerOptions = {
            numZoomLevels: 20,
            attribution: "",
            visibility: false,
        };
        if (typeof google != "undefined") {
            googleLayerOptions.type = google.maps.MapTypeId.HYBRID;
            googleLayer = new OpenLayers.Layer.Google(
                "Google Hybrid",
                googleLayerOptions
            );
        }

        var plotsLayer = createPlotsVectorLayer();
        var sampleGridLayer = createSampleGridVectorLayer();
        var samplePointsLayer = createSamplePointsVectorLayer();
        var TrackLayer = createTrackVectorLayer();

        var locMarker = new OpenLayers.Layer.Markers("locationMarker");
        var locAccuracy = new OpenLayers.Layer.Vector("locationAccuracy");

        var pinsLayer = new OpenLayers.Layer.Markers("pins", {
            strategies: [new OpenLayers.Strategy.Cluster({ distance: 25 })],
        });

        var leafPinsLayer = new OpenLayers.Layer.Markers("leafPins", {
            strategies: [new OpenLayers.Strategy.Cluster({ distance: 25 })],
        });

        map.addLayers([
            geoScanLayer,
            bingLayer,
            googleLayer,
            locAccuracy,
            locMarker,
            plotsLayer,
            pinsLayer,
            leafPinsLayer,
            sampleGridLayer,
            samplePointsLayer,
            TrackLayer,
        ]);

        //Moving plots vector layer to top position on add layer to map
        map.events.register("addlayer", this, function (evt) {
            if (plotsLayer) {
                map.raiseLayer(plotsLayer, map.layers.length);
            }

            if (sampleGridLayer) {
                map.raiseLayer(sampleGridLayer, map.layers.length);
            }

            if (samplePointsLayer) {
                map.raiseLayer(samplePointsLayer, map.layers.length);
            }

            if (pinsLayer) {
                map.raiseLayer(pinsLayer, map.layers.length);
            }
            if (TrackLayer) {
                map.raiseLayer(TrackLayer, map.layers.length);
            }
            if (evt.layer.name == "locationTrack") {
                map.raiseLayer(evt.layer, map.layers.length);
            }
            if (leafPinsLayer) {
                map.raiseLayer(leafPinsLayer, map.layers.length);
            }
            if (locMarker) {
                map.raiseLayer(locMarker, map.layers.length);
            }
        });

        var extent = JSON.parse(localStorage.getItem("ORGANIZATION_EXTENT"));
        map.setCenter(
            new OpenLayers.Bounds(extent).getCenterLonLat(),
            Constants.zoom_level
        );

        var selectCtrl = new MapControlSelect(map, plotsLayer);
        selectCtrl.activate();

        var measureCtrl = new MapControlMeasure(map);

        this.pinsClick = new OpenLayers.Control.Click({
            handler: onPinAdd,
        });
        map.addControl(this.pinsClick);

        function onPinAdd(e) {
            var lonlat = this.map.getLonLatFromPixel(e.xy);

            Backbone.trigger("gs:pinAdd", lonlat);
        }

        function createPlotsVectorLayer() {
            var page_name = localStorage.getItem("page_name");
            var defaultLayerOptions = {
                strokeColor: "#0083c9",
                strokeWidth: 2,
                fillOpacity: 0,
                labelOutlineColor: "#eeeeee",
                labelOutlineWidth: "3px",
                fontSize: "13px",
                fontWeight: "bold",
                labelAlign: "cm",
            };

            var rules = [
                new OpenLayers.Rule({
                    filter: new OpenLayers.Filter.Comparison({
                        type: OpenLayers.Filter.Comparison.EQUAL_TO,
                        property: "isComplete",
                        value: true,
                    }),
                    symbolizer: {
                        fillColor: "green",
                    },
                }),
                new OpenLayers.Rule({
                    filter: new OpenLayers.Filter.Comparison({
                        type: OpenLayers.Filter.Comparison.EQUAL_TO,
                        property: "isComplete",
                        value: false,
                    }),
                    symbolizer: {
                        fillColor: "#fcee21",
                    },
                }),
            ];
            if (page_name == "task_plots") {
                defaultLayerOptions.fillOpacity = 0.8;
                defaultLayerOptions.label = "${plotName}";
            }
            var defaultStyle = new OpenLayers.Style(defaultLayerOptions);
            if (page_name == "task_plots") {
                defaultStyle.addRules(rules);
            }
            var selectStyle = new OpenLayers.Style(
                {
                    strokeColor: "#074069",
                    fillColor: "#ffffff",
                    strokeWidth: 3,
                    fillOpacity: 0.3,
                    label: "${label}",
                    labelOutlineColor: "#eeeeee",
                    labelOutlineWidth: "3px",
                    fontSize: "13px",
                    fontWeight: "bold",
                    labelAlign: "cm",
                },
                {
                    context: {
                        label: function (feature) {
                            if (
                                !feature.attributes.hasOwnProperty("label") ||
                                map.resolution > 5
                            ) {
                                return "";
                            }
                            return feature.attributes.label;
                        },
                    },
                }
            );

            var plotsVectorLayer = new OpenLayers.Layer.Vector(
                "plots_vector_layer",
                {
                    styleMap: new OpenLayers.StyleMap({
                        default: defaultStyle,
                        select: selectStyle,
                    }),
                }
            );

            return plotsVectorLayer;
        }

        function createSampleGridVectorLayer() {
            var sampleGridVectorLayer = new OpenLayers.Layer.Vector(
                "sample_grid_vector_layer",
                {
                    styleMap: new OpenLayers.StyleMap(
                        new OpenLayers.Style({
                            strokeColor: "#0083c9",
                            strokeWidth: 2,
                            fillOpacity: 0,
                            label: "${label}",
                            labelOutlineColor: "#eeeeee",
                            labelOutlineWidth: "3px",
                            fontSize: "13px",
                            fontWeight: "bold",
                        })
                    ),
                }
            );

            return sampleGridVectorLayer;
        }

        function createTrackVectorLayer() {
            var trackVectorLayer = new OpenLayers.Layer.Vector(
                "track_vector_layer",
                {
                    styleMap: new OpenLayers.StyleMap(
                        new OpenLayers.Style({
                            strokeColor: "#f70202",
                            strokeWidth: 4,
                            strokeOpacity: 0.4,
                            fillOpacity: 0,
                        })
                    ),
                }
            );

            return trackVectorLayer;
        }

        function createSamplePointsVectorLayer() {
            var samplePointsVectorLayer = new OpenLayers.Layer.Vector(
                "sample_points_vector_layer",
                {
                    styleMap: new OpenLayers.StyleMap(
                        new OpenLayers.Style({
                            strokeColor: "#0083c9",
                            strokeWidth: 2,
                            fillOpacity: 0,
                            label: "${label}",
                            labelOutlineColor: "#eeeeee",
                            labelOutlineWidth: "3px",
                            fontSize: "13px",
                            fontWeight: "bold",
                        })
                    ),
                }
            );

            return samplePointsVectorLayer;
        }

        function pulsate(feature) {
            var point = feature.geometry.getCentroid(),
                bounds = feature.geometry.getBounds(),
                radius = Math.abs((bounds.right - bounds.left) / 2),
                count = 0,
                grow = "up";

            var resize = function () {
                if (count > 16) {
                    clearInterval(window.resizeInterval);
                }
                var interval = radius * 0.03;
                var ratio = interval / radius;
                switch (count) {
                    case 4:
                    case 12:
                        grow = "down";
                        break;
                    case 8:
                        grow = "up";
                        break;
                }
                if (grow !== "up") {
                    ratio = -Math.abs(ratio);
                }
                feature.geometry.resize(1 + ratio, point);
                locAccuracy.drawFeature(feature);
                count++;
            };
            window.resizeInterval = window.setInterval(
                resize,
                50,
                point,
                radius
            );
        }

        //public
        this.olMap = map;
        this.selectControl = selectCtrl;
        this.measureControl = measureCtrl;
        this.plotsVectorLayer = plotsLayer;
        this.trackVectorLayer = TrackLayer;
        this.sampleGridVectorLayer = sampleGridLayer;
        this.samplePointsVectorLayer = samplePointsLayer;
        this.locationMarker = locMarker;
        this.locationAccuracy = locAccuracy;
        this.leafPinsLayer = leafPinsLayer;

        this.createImageLayer = function (feature, imgUrl) {
            var imageLayer = new OpenLayers.Layer.Image(
                "ImageLayer",
                imgUrl,
                feature.geometry.getBounds(),
                new OpenLayers.Size(1, 1),
                {
                    isBaseLayer: false,
                }
            );

            return imageLayer;
        };

        this.removeImageLayer = function () {
            selectCtrl.unselectAll();

            var features = plotsLayer.features;
            features.forEach(function (feature) {
                if (
                    feature.attributes.currentImage &&
                    feature.attributes.currentImageLayer
                ) {
                    map.removeLayer(
                        map.getLayersBy(
                            "id",
                            feature.attributes.currentImageLayer
                        )[0]
                    );
                    feature.attributes.currentImage = undefined;
                    feature.attributes.currentImageLayer = undefined;
                }
            });
        };

        this.showLocationMarker = function (
            position,
            puls,
            withZoom,
            withPan,
            rotation = 0
        ) {
            locMarker.clearMarkers();
            locAccuracy.removeAllFeatures();

            var size = new OpenLayers.Size(25, 41);
            var offset = new OpenLayers.Pixel(-(size.w / 2), -size.h);
            var icon = new OpenLayers.Icon(
                "themes/images/location-icon.png",
                size,
                offset
            );
            locMarker.addMarker(
                new OpenLayers.Marker(
                    new OpenLayers.LonLat(
                        position.coords.longitude,
                        position.coords.latitude
                    ).transform("EPSG:4326", "EPSG:3857"),
                    icon
                )
            );

            var circle = new OpenLayers.Feature.Vector(
                OpenLayers.Geometry.Polygon.createRegularPolygon(
                    new OpenLayers.Geometry.Point(
                        position.coords.longitude,
                        position.coords.latitude
                    ).transform("EPSG:4326", "EPSG:3857"),
                    position.coords.accuracy / 2,
                    40,
                    0
                ),
                {},
                {
                    fillColor: "#fff",
                    fillOpacity: 0.25,
                    strokeWidth: 1,
                    strokeColor: "#0083c9",
                }
            );

            locAccuracy.addFeatures([circle]);

            if (withZoom) {
                map.zoomToExtent(locAccuracy.getDataExtent());
            }

            if (withPan) {
                map.panTo(
                    new OpenLayers.LonLat(
                        position.coords.longitude,
                        position.coords.latitude
                    ).transform("EPSG:4326", "EPSG:3857")
                );
            }

            if (puls === true) {
                pulsate(circle);
            }
        };

        this.addPlots = function (plotsData) {
            var gj = new OpenLayers.Format.GeoJSON();

            var features = [];

            var authData = JSON.parse(localStorage.getItem("authData"));
            var finishedPlots =
                JSON.parse(
                    localStorage.getItem("finishedPlots" + authData.user_id)
                ) || [];

            plotsData.forEach(function (plot) {
                var plotName = plot.plot_name || Localization.no_name;
                var sampleType = "";
                var plot_sample_types_array = plot.sample_type_json;
                for (var type in plot_sample_types_array) {
                    sampleType +=
                        plot_sample_types_array[type].short_name + ";";
                }

                var isComplete = false;
                if (finishedPlots.indexOf(plot.plot_gid) != -1) {
                    isComplete = true;
                }

                var feature = new OpenLayers.Feature.Vector(
                    gj.read(plot.plot_geom, "Geometry"),
                    {
                        id: plot.plot_gid,
                        label: sampleType,
                        isComplete: isComplete,
                        plotName: plotName,
                    }
                );

                features.push(feature);
            });

            plotsLayer.removeAllFeatures();
            plotsLayer.addFeatures(features);
        };

        this.addSampleGrid = function (plotsData) {
            var in_options = {
                internalProjection: "EPSG:3857",
                externalProjection: "EPSG:3857",
            };
            var gj = new OpenLayers.Format.GeoJSON(in_options);

            var features = [];

            plotsData.forEach(function (plot) {
                if (plot.sample_grid !== undefined) {
                    var user_id = localStorage.getItem("user_id");
                    var task_id = localStorage.getItem(
                        "viewed_task_id" + user_id
                    );
                    sampledTasksData =
                        JSON.parse(
                            localStorage.getItem("sampledTasks" + user_id)
                        ) || {};

                    plot.sample_grid.features.forEach(function (obj) {
                        if (
                            sampledTasksData[task_id] == undefined ||
                            sampledTasksData[task_id][plot.plot_gid] ==
                                undefined ||
                            !sampledTasksData[task_id][
                                plot.plot_gid
                            ].hasOwnProperty(obj.properties.sample_id)
                        ) {
                            fillOpacity = 0.3;
                        } else {
                            fillOpacity = 0.8;
                        }

                        if (
                            _.contains(
                                plot.leaf_sample_cells,
                                obj.properties.sample_id.toString()
                            )
                        ) {
                            fillColor = "#FF3366";
                        } else {
                            fillColor =
                                obj.properties.for_sampling === true
                                    ? "#fcee21"
                                    : "#008cff";
                        }

                        var feature = new OpenLayers.Feature.Vector(
                            gj.read(obj.geometry, "Geometry"),
                            {
                                label: obj.properties.sample_id,
                                type: obj.geometry.type,
                            },
                            {
                                fillColor: fillColor,
                                fillOpacity: fillOpacity,
                                strokeColor: "#000000",
                            }
                        );

                        features.push(feature);
                    });
                }
            });

            sampleGridLayer.removeAllFeatures();
            sampleGridLayer.addFeatures(features);
        };

        this.addSamplePoints = function (plotsData) {
            var gj = new OpenLayers.Format.GeoJSON();

            var features = [];
            var user_id = localStorage.getItem("user_id");
            var task_id = localStorage.getItem("viewed_task_id" + user_id);
            var plot_id = localStorage.getItem("viewed_plot_id" + user_id);

            sampledTasksData =
                JSON.parse(localStorage.getItem("sampledTasks" + user_id)) ||
                {};

            plotsData.forEach(function (plot) {
                if (plot.sample_points !== undefined) {
                    plot.sample_points.features.forEach(function (point) {
                        if (
                            sampledTasksData[task_id] == undefined ||
                            sampledTasksData[task_id][plot_id] == undefined ||
                            !sampledTasksData[task_id][plot_id].hasOwnProperty(
                                point.properties.sample_id
                            )
                        ) {
                            pointStrokeColor = "#ff0000";
                        } else {
                            pointStrokeColor = "#00ff00";
                        }
                        var feature = new OpenLayers.Feature.Vector(
                            OpenLayers.Geometry.Polygon.createRegularPolygon(
                                new OpenLayers.Geometry.Point(
                                    point.geometry.coordinates[0],
                                    point.geometry.coordinates[1]
                                ),
                                1,
                                40,
                                0
                            ),
                            {
                                gid: point.properties.gid,
                            },
                            {
                                fillColor: "#fff",
                                fillOpacity: 0.25,
                                strokeWidth: 1,
                                strokeColor: pointStrokeColor,
                                label: point.properties.sample_id.toString(),
                                labelOutlineColor: "#c0c0c0",
                            }
                        );

                        features.push(feature);
                    });
                }
            });

            samplePointsLayer.removeAllFeatures();
            samplePointsLayer.addFeatures(features);
        };

        this.openPopup = function (pin) {
            if (openedPopupPin && openedPopupPin.id == pin.id) {
                openedPopupPin = undefined;
                return;
            }

            openedPopupPin = pin;
            var pinlonlat = new OpenLayers.LonLat(pin.lon, pin.lat);

            var pixel = map.getPixelFromLonLat(pinlonlat);
            pixel.x -= 47;

            var lonlat = map.getLonLatFromViewPortPx(pixel);

            var comment = pin.comment;
            if (pin.comment.length > 50) {
                comment = pin.comment.substr(0, 6) + "...";
            }

            // A popup with some information about our location
            var popup = new OpenLayers.Popup(
                "Popup",
                lonlat,
                null,
                '<div class="triangle-border top"><ul class="ui-listview ui-corner-all no-border"><li class="ui-first-child ui-last-child"><span style="white-space:normal;">' +
                    pin.title +
                    '</span><p style="white-space:normal;">' +
                    comment +
                    "</p></li></ul></div>",
                null
            );

            popup.backgroundColor = "transparent";

            popup.panMapIfOutOfView = true;
            popup.keepInMap = true;
            popup.autoSize = true;
            // and add the popup to it.
            map.addPopup(popup);
        };

        this.addPins = function (pinsData) {
            var self = this;

            _.each(pinsData, function (pin) {
                var lonlat = new OpenLayers.LonLat(pin.lon, pin.lat);
                var size = new OpenLayers.Size(32, 32);
                var offset = new OpenLayers.Pixel(-(size.w / 2), -size.h);
                var icon = new OpenLayers.Icon(
                    "themes/images/pin-icon.png",
                    size,
                    offset
                );
                var marker = new OpenLayers.Marker(lonlat, icon);
                marker.attributes = pin;
                pinsLayer.addMarker(marker);
            });
        };

        this.removeAllPins = function () {
            var pins = pinsLayer.markers.slice();
            _.each(pins, function (marker) {
                pinsLayer.removeMarker(marker);
            });

            while (map.popups.length > 0) {
                map.removePopup(map.popups[0]);
                openedPopupPin = undefined;
            }
        };

        this.addNewLeafPin = function (pin) {
            var lonlat = new OpenLayers.LonLat(pin.lon, pin.lat);
            var size = new OpenLayers.Size(24, 36);
            var offset = new OpenLayers.Pixel(-(size.w / 2), -size.h);

            if (pin.is_my_pin == true) {
                var icon = new OpenLayers.Icon(
                    "themes/images/pin-my-leaf-icon.png",
                    size,
                    offset
                );
            } else {
                var icon = new OpenLayers.Icon(
                    "themes/images/pin-leaf-icon.png",
                    size,
                    offset
                );
            }

            var leafMarker = new OpenLayers.Marker(lonlat, icon);
            leafMarker.attributes = pin;
            leafMarker.setOpacity(0.7);
            leafPinsLayer.addMarker(leafMarker);

            leafMarker.events.register("touchend", map, function (e) {
                while (map.popups.length > 0) {
                    map.removePopup(map.popups[0]);
                }
                self.openPopup(pin);
            });
        };

        this.addLeafPins = function (plotsData) {
            var self = this;
            var pinsData = _.where(PinsCollection.getAllPins(), {
                sopr_id: plotsData[0].sopr_id,
            });

            if (pinsData != undefined) {
                _.each(pinsData, this.addNewLeafPin);
            } else {
                PinsCollection.setPinsData([]);
            }
        };

        this.selectPlot = function (plot_id) {
            var features = plotsLayer.getFeaturesByAttribute(
                "id",
                parseInt(plot_id)
            );

            if (features.length > 0) {
                var extent = features[0].geometry.getBounds();
                var centroid = features[0].geometry.getCentroid();

                if (!centroid) {
                    return;
                }

                map.zoomToExtent(extent);

                setTimeout(function () {
                    selectCtrl.selectBox(centroid.getBounds());
                }, 500);
            }
        };

        this.setMapType = function () {
            var map_type = localStorage.getItem("map_type");

            map.zoomDuration = 20;
            geoScanLayer.setVisibility(false);
            bingLayer.setVisibility(false);
            googleLayer.setVisibility(false);

            if (map_type == "google") {
                map.zoomDuration = 0;
                googleLayer.setVisibility(true);
                map.setBaseLayer(googleLayer);
            } else if (map_type == "bing") {
                bingLayer.setVisibility(true);
                map.setBaseLayer(bingLayer);
            } else if (map_type == "none") {
                geoScanLayer.setVisibility(false);
            } else {
                geoScanLayer.setVisibility(true);
                map.setBaseLayer(geoScanLayer);
            }
        };

        this.displayRecordedTrack = function () {
            var gj = new OpenLayers.Format.GeoJSON();
            var user_id = localStorage.getItem("user_id");
            var task_id = localStorage.getItem("viewed_task_id" + user_id);
            var plot_id = localStorage.getItem("viewed_plot_id" + user_id);

            trackData =
                JSON.parse(localStorage.getItem("sampledTasks" + user_id)) ||
                {};

            var features = [];

            if (
                trackData[task_id] == undefined ||
                trackData[task_id][plot_id] == undefined
            ) {
                return;
            }
            for (i in trackData[task_id][plot_id]) {
                var cellTrack = trackData[task_id][plot_id][i];

                var feature = new OpenLayers.Feature.Vector(
                    gj.read(cellTrack.track.d2, "Geometry"),
                    {
                        id: i,
                    }
                );

                features.push(feature);
            }

            TrackLayer.removeAllFeatures();
            TrackLayer.addFeatures(features);
        };

        this.removeRecordedTrack = function () {
            TrackLayer.removeAllFeatures();
        };

        this.updateCellStylesOnCompletingSample = function (cellLabel) {
            var samplePoints = this.samplePointsVectorLayer.features;
            var selectedCellSamplePoint = _.find(
                samplePoints,
                function (feature) {
                    return feature.style.label == cellLabel;
                }
            );
            this.samplePointsVectorLayer.getFeatureById(
                selectedCellSamplePoint.id
            ).style.strokeColor = "#00ff00";

            var sampleCells = _.filter(
                this.sampleGridVectorLayer.features,
                function (feature) {
                    return feature.attributes.label == cellLabel;
                }
            );
            for (i = 0; i < sampleCells.length; i++) {
                this.sampleGridVectorLayer.getFeatureById(
                    sampleCells[i].id
                ).style.fillOpacity = 0.8;
            }

            this.samplePointsVectorLayer.redraw();
            this.sampleGridVectorLayer.redraw();

            return selectedCellSamplePoint;
        };
    };

    return Map;
});

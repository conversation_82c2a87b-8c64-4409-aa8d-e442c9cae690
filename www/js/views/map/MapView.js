var MapView;
var navMarker;
/**
 * MapView type definition
 * @typedef {Object} MapView
 * @property {number[]} task_treatment_type
 */
define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'views/menu/MenuView',
    'text!templates/map/mapTemplate.html',
    'views/widgets/MapToolBarWidget',
    'views/widgets/WorkflowInputWidget',
    'views/widgets/AddLeafPinInputWidget',
    'views/map/Map',
    'views/widgets/TakePhoto',
    'collections/ExternalLocationProviderCollection',
    'jquerymobile',
    'services/SampleNumbersService'
], function ($, _, Backbone, Localization, MenuView, mapTemplate, MapToolBarWidget, WorkflowInputWidget, AddLeafPinInputWidget, Map, TakePhoto, ExternalLocationProvider, jquerymobile, SampleNumbersService) {

    MapView = Backbone.View.extend(
        {
        events: {
            'click #panTool': 'initPan',
            'click #showInfo': 'showInfo',
            'click #setHeading': 'pinTool',
        },
        user_id: localStorage.getItem('user_id'),
        el: $("#page"),
        viewport: {},
        map: undefined,
        view: undefined,
        bingLayer: undefined,
        geoScanLayer: undefined,
        isLocating: false,
        isIntervalSet: false,
        plotsData: [],
        samplesData: undefined,
        task_id: null,
        plot_id: null,
        task_treatment_type: undefined,
        leaf_cells:[],
        geoJSON: undefined,
        trackAltitudes:[],
        samplePausedAltitudes:[],
        minUpdateDistance: 1,
        destinationPosition: undefined,
        destinationBearing: undefined,
        destinationDistance: undefined,
        currentPosition: undefined,
        prevPosition: undefined,
        watchId: undefined,
        currentHeading: undefined,
        isCompassActive: false,

        isShallowSoilSample: false,
        isDeepSoilSample: false,
        isLeafSample: false,
        tmpSampleImages:[],
        locationProvider: undefined,
        lastPoint: undefined,
        autoZoom: false,
        autoPan: false,
        isSampleTakingOnPause: false,
        samplePausedTrack: undefined,
        initialize: function () {
            this.undelegateEvents();
            var self = this;

            Backbone.on('gs:mapChange', function() {
                this.mapObj.setMapType();
            }, self);

            Backbone.on('gs:pinAdd', function(lonlat) {
                this.onPinAdd(lonlat);
            }, self);

            Backbone.on('gs:geoLocate', function() {
                this.geoLocate();
            }, self);

            Backbone.on('gs:zoomToPlotsExtent', function() {
                this.zoomToPlotsExtent();
            }, self);

            Backbone.on('gs:leafPinAdd', function(leafPinParams) {
                this.newLeafPin(leafPinParams);
            }, self);

            this.viewport = {
                width: $(window).width(),
                height: $(window).height() - 112
            };
            self.geoJSON = new OpenLayers.Format.GeoJSON();
        },
        render: function (task_id, plot_id) {
            this.task_id = parseInt(task_id);
            this.plot_id = parseInt(plot_id);

            var self = this;

            var toolbarData = {
                'isMapPage': true
            }
            MapToolBarWidget.render(toolbarData);
            
            var taskData = null;
            fileStorage.getItem('tasks_data_' + this.user_id, function(tasks) {
                taskData = tasks.find(task => task.id == self.task_id);
 
                if(taskData?.plots?.length) {
                    self.plotsData = taskData.plots;
                } else {
                    var plots = [];

                    tasks.forEach(function (task) {
                        plots = [...plots, ...task.plots];
                    });

                    self.plotsData = plots;
                    taskData = tasks.find(task => task.plots.some(plot => plot.plot_gid == self.plot_id));
                }
            });

            var plot = this.plotsData.find(plot => plot.plot_gid == self.plot_id);
            this.task_treatment_type = plot.sample_type_json;
            var leaf_cells_string = plot.leaf_sample_cells;
            this.leaf_cells = leaf_cells_string.split(',');

            var data = {
                'viewport': this.viewport,
                'Localization': Localization,
                'PlotData': plot,
                'treatment_types': this.task_treatment_type,
                'defaultActiveTab': 0 
            };

            if (typeof mapTemplate === 'string') {
                mapTemplate = _.template(mapTemplate);
            }

            this.$el.append(mapTemplate(data)).trigger('create');

            var workflowInitializeData = {
                'treatment_types': this.task_treatment_type,
                'defaultActiveTab': 0 
            };

            WorkflowInputWidget.$el = $('#workflow-popup-container');
            WorkflowInputWidget.render(workflowInitializeData);
            WorkflowInputWidget.delegateEvents();

            AddLeafPinInputWidget.$el = $('#add-leaf-pin-popup-container');
            AddLeafPinInputWidget.render({});
            AddLeafPinInputWidget.delegateEvents();

            $('h1.menu-title').text(Localization.map);

            this.mapObj = new Map();
            this.mapObj.setMapType();

            if (this.plotsData.length === 0)
            {
                var n = noty({
                    layout: 'center',
                    theme: 'relax', // or 'relax'
                    type: 'alert',
                    text: Localization.plots_sync_advice, // can be html or string
                    dismissQueue: false, // If you want to use queue feature set this true
                    template: '<div class="noty_message"><span class="noty_text"></span><div class="noty_close"></div></div>',
                    animation: {
                        open: {height: 'toggle'}, // or Animate.css class names like: 'animated bounceInLeft'
                        close: {height: 'toggle'}, // or Animate.css class names like: 'animated bounceOutLeft'
                        easing: 'swing',
                        speed: 500 // opening & closing animation speed
                    },
                    timeout: false, // delay for closing event. Set false for sticky notifications
                    force: false, // adds notification to the beginning of queue when set to true
                    modal: true,
                    maxVisible: 5, // you can set max visible notification for dismissQueue true option,
                    killer: false, // for close all notifications before show
                    closeWith: ['click', 'backdrop'], // ['click', 'button', 'hover', 'backdrop'] // backdrop click will close all notifications
                    buttons: false // an array of buttons
                });
            }
            else {
                self.loadPlots(plot_id);
            }

            this.setRoadTo();
            this.geoLocateBtn = $('#geoLocate');

            $('#compass-container').hide();
            $('#popup-workflow-stop').on('click', '.scan-barcode-btn', $.proxy(this.scanBarcode, this));

            $('#start-sample-taking').off('click').on('click', $.proxy(this.checkPosition, this));

            $('#toggle-sample-taking').off('click').on('click', $.proxy(this.toggleSampleTaking, this));
            $('#popup-end-sample-taking').off('click').on('click', $.proxy(this.endSampleTaking, this));

            $('#popup-workflow-stop-ok').off('click').on('click', $.proxy(this.onAddSampleClick, this));
            $('#popup-workflow-stop-cancel').off('click').on('click', function(e) {
                $("#popup-workflow-stop").popup('close');
                $("#popup-workflow-stop").hide();
                $('#loading-popup').popup('close');$.mobile.loading('hide');
                $("#toggle-sample-taking" ).removeClass("ui-disabled");
                $("#end-sample-taking" ).removeClass("ui-disabled");
            });

            $('#end-sample-taking').off('click').on('click', $.proxy(this.pauseStopSampleTaking, this));

            var treatment_type_label = '';

            for (var type in this.task_treatment_type) {
                treatment_type_label += this.task_treatment_type[type].short_name + '; ';
            }

            var treatment_type_label = this.task_treatment_type.map(treatmentType =>
                 treatmentType.short_name
            ).join('; ');

            var plotName = data.PlotData.plot_name || Localization.no_name;

            $('#plotInfo .ui-block-b > ul').empty();
            $("#plotInfo .ui-block-b > ul").append('<li>' + taskData.order_name + '</li>');
            $("#plotInfo .ui-block-b > ul").append('<li>' + plotName +'</li>');
            $("#plotInfo .ui-block-b > ul").append('<li>' + (data.PlotData.ekatte ? data.PlotData.ekatte : ' - ') + '</li>');
            $("#plotInfo .ui-block-b > ul").append('<li>' + treatment_type_label + '</li>');
            $('#plotInfo .ui-block-b > ul').listview("refresh").trigger('create').trigger('resize');

            sampledTasksData = JSON.parse(localStorage.getItem('sampledTasks' + this.user_id)) || {};
            if (sampledTasksData[this.task_id] !== undefined && sampledTasksData[this.task_id].hasOwnProperty(this.plot_id)) {
                $('#start-sample-taking').hide();
                $('#workflow-list').show();
                $('#toggle-sample-taking').addClass('ui-btn-d');
                $('#toggle-sample-taking').removeClass('ui-btn-c');
                $('#toggle-sample-taking').html(Localization.continue);

                $('#end-sample-taking').data('action', 'end');
                $('#end-sample-taking').html(Localization.end);
            }

            $(window).on('resize', function() {
                $('#map').css({
                    width: $(window).width(),
                    height: $(window).height() - 112
                });

                self.mapObj.olMap.updateSize();
            });

            this.$el.trigger('create').trigger('resize');

            this.initPhotoControls();
            this.initDisplayMarkerOptions();
            this.initLocationProvider();
        },
        zoomToPlotsExtent: function () {
            var extent = this.mapObj.plotsVectorLayer.getDataExtent();
            if(!extent) {
                extent = JSON.parse(localStorage.getItem('ORGANIZATION_EXTENT'));
            }
            this.mapObj.olMap.zoomToExtent(extent);
        },
        toggleGPSMeasure: function(event) {
            var self = this;
            var button = $(event.currentTarget);

            if(button.data('action') === 'start') {
                self.GPSDrawingEnabled = true;

                button.data('action', 'stop');
                button.html(Localization.stop);
            }
            else if(button.data('action') === 'stop') {
                self.GPSDrawingEnabled = false;

                button.data('action', 'start');
                button.html(Localization.start);
            }
        },
        initPan: function (event) {
            $('.map-toolbar .fi').removeClass('current');
            $(event.currentTarget).addClass('current');

            if(this.drawingType && this.drawingType === 'gps') {
                this.stopGPSMeasure();
            }
            this.drawingType = undefined;

            this.mapObj.measureControl.LineString.deactivate();
            this.mapObj.measureControl.Polygon.deactivate();
            this.mapObj.pinsClick.deactivate();
            this.mapObj.selectControl.activate();
        },
        pinTool: function (event) {
            var self = this;

            $('.map-toolbar .fi').removeClass('current');

            if(self.isCompassActive === false) {
                $(event.currentTarget).addClass('current');
                self.isCompassActive = true;
                self.isLocating = true;
            }else{
                $(event.currentTarget).removeClass('current');
                self.isCompassActive = false;
                self.mapObj.pinsClick.deactivate();
                self.mapObj.removeAllPins();
                $('#compass-container').hide();
                return;
            }
            if(self.drawingType && self.drawingType === 'gps') {
                self.stopGPSMeasure();
            }
            self.drawingType = undefined;

            self.mapObj.measureControl.LineString.deactivate();
            self.mapObj.measureControl.Polygon.deactivate();
            self.mapObj.selectControl.deactivate();

            self.mapObj.pinsClick.activate();
        },
        onPinAdd: function(lonlat) {
            var self = this;

            var currentPin = {};
            self.initHeadingNavigation();
            currentPin.lon = lonlat.lon;
            currentPin.lat = lonlat.lat;
            $('#compass-container').show();
            self.updateDestination(currentPin);
            pins = [currentPin];

            this.mapObj.removeAllPins();
            this.mapObj.addPins(pins);
        },
        geoLocate: function () {
            if(this.isLocating) {
                this.clearWatch();
                this.stopGPSMeasure();
                this.isLocating = false;
                return;
            }

            if(this.GPSMeasureID || (this.drawingType && this.drawingType === 'gps')) {
                return;
            }

            if(window.watchPositionID) {
                this.clearWatch();
                return;
            }

            this.isLocating = true;
            this.pulseGPSIcon('on');

            var self = this;
            // onSuccess Callback
            // This method accepts a Position object, which contains the
            // current GPS coordinates
            //
            var onSuccess = function (position) {
                self.isLocating = false;
                self.pulseGPSIcon('off');
                self.mapObj.showLocationMarker(position, true, self.autoZoom, self.autoPan);

                try {
                    navigator.notification.confirm(
                        Localization.location_tracking_info,
                        function (button) {
                            if (button == "2") {
                                self.geoLocateBtn.css('border-color', '#22A84C');
                                self.watchPosition();
                                self.isLocating = true;
                            }
                        },
                        Localization.location_tracking,
                        [Localization.no, Localization.yes]
                    );
                }
                catch(err) {
                    console.log(err);
                }
            };

            // onError Callback receives a PositionError object
            //
            function onError(error) {
                self.isLocating = false;
                self.pulseGPSIcon('off');
                errorMessage = Localization.location_error;
                if(error.message == 'no data in state') {
                    errorMessage = Localization.bluetooth_connection_fail;
                }
                navigator.notification.alert(
                    errorMessage,    // message
                    function() {},                  // callback
                    Localization.error,             // title
                    Localization.ok                 // buttonName
                );
            }

            this.locationProvider.getCurrentPosition(onSuccess, onError, {enableHighAccuracy: true, timeout:60000});
        },
        watchPosition: function() {
            this.pulseGPSIcon('on');
            var self = this;
            // onSuccess Callback
            //   This method accepts a `Position` object, which contains
            //   the current GPS coordinates
            //
            function onSuccess(position) {
                self.mapObj.showLocationMarker(position, false, self.autoZoom, self.autoPan);
                self.prevPosition = self.currentPosition;
                self.currentPosition = new OpenLayers.LonLat(position.coords.longitude, position.coords.latitude);

                if(self.prevPosition && OpenLayers.Spherical.computeDistanceBetween(self.prevPosition, self.currentPosition) < self.minUpdateDistance) return;

                self.updatePositions();
            }

            // onError Callback receives a PositionError object
            //
            function onError(error) {
                console.log('code: ' + error.code + '\n' + 'message: ' + error.message + '\n');
            }

            // Options: throw an error if no update is received every 30 seconds.
            //
            window.watchPositionID = self.locationProvider.watchPosition(onSuccess, onError, {timeout: 60000, enableHighAccuracy: true});
        },
        clearWatch: function() {
            this.pulseGPSIcon('off');
            this.geoLocateBtn.css('border-color', '#c5c5c5');
            this.locationProvider.clearWatch(window.watchPositionID);
            window.watchPositionID = undefined;
        },
        measureByGPSWatch: function() {
            var self = this;
            var iterator = 0;
            // onSuccess Callback
            //   This method accepts a `Position` object, which contains
            //   the current GPS coordinates
            //
            function onSuccess(position) {  
                if(iterator < 1) {
                    self.mapObj.showLocationMarker(position, true, self.autoZoom, self.autoPan);
                }
                else {
                    self.mapObj.showLocationMarker(position, false, self.autoZoom, self.autoPan);
                }

                if(!self.GPSDrawingEnabled) {
                    return;
                }

                var point = new OpenLayers.Geometry.Point(position.coords.longitude, position.coords.latitude).transform('EPSG:4326', 'EPSG:3857');

                if(iterator < 1) {
                    self.lastPoint = point;

                    self.mapObj.measureControl.GPS.track.addPoint(point);
                    self.trackAltitudes.push(position.coords.altitude);
                    self.mapObj.measureControl.GPS.locationTrack.redraw(true);
                }
                else {

                    if(self.lastPoint.distanceTo(point) >= 2) {
                        self.mapObj.measureControl.GPS.track.addPoint(point);
                        self.trackAltitudes.push(position.coords.altitude);

                        self.mapObj.measureControl.GPS.locationTrack.redraw(true);
                        self.lastPoint = point;
                    }
                }
                iterator++;
            }

            // onError Callback receives a PositionError object
            //
            function onError(error) {
                console.log('code: '    + error.code    + '\n' + 'message: ' + error.message + '\n');
            }

            // Options: throw an error if no update is received every 30 seconds.
            //
            this.GPSMeasureID = this.locationProvider.watchPosition(onSuccess, onError, {timeout: 60000, enableHighAccuracy: true});
            this.pulseGPSIcon('on');
        },
        stopGPSMeasure: function(keepMarker, keepFeatures) {
            var button = $('#toggleGPSMeasure');
            button.data('action', 'start');
            button.html(Localization.start);

            this.GPSDrawingEnabled = false;

            $('#watchPositionInfo').hide();
            $('#watchPositionText').html('');

            if(this.GPSMeasureID) {
                this.pulseGPSIcon('off');
                this.locationProvider.clearWatch(this.GPSMeasureID);
                this.GPSMeasureID = undefined;
            }

            if (!keepMarker) {
                this.mapObj.locationMarker.clearMarkers();
            }
            if (!keepFeatures) {
                this.mapObj.locationAccuracy.removeAllFeatures();
                this.clearGPSMeasure();
            }
        },
        clearGPSMeasure: function() {
            this.mapObj.measureControl.GPS.locationTrack.removeAllFeatures();
            this.mapObj.measureControl.GPS.locationTrack.redraw(true);
        },
        pulseGPSIcon: function(toggle) {
            var self = this;
            if(toggle === 'on' && self.isIntervalSet === false) {
                this.geoLocateBtn.css('color', '#c5c5c5');
                var isBlue = false;
                self.isIntervalSet = true;
                window.GPSinterval = setInterval(function() {

                    if(isBlue) {
                        self.geoLocateBtn.css('color', '#c5c5c5');
                        isBlue = false;
                    }
                    else {
                        self.geoLocateBtn.css('color', '#22A84C');
                        isBlue = true;
                    }
                }, 500);
            }
            else if(toggle === 'off' && self.isIntervalSet === true) {
                self.isIntervalSet = false;
                clearInterval(window.GPSinterval);
                this.geoLocateBtn.css('color', '#5c5c5c');
            }
        },
        loadPlots: function(plot_id) {
            plot_id = parseInt(plot_id);
            var plots = _.where(this.plotsData, {plot_gid: plot_id});

            this.mapObj.addPlots(plots);
            this.mapObj.addSampleGrid(plots);
            this.mapObj.addSamplePoints(plots);
            
            this.mapObj.addLeafPins(plots);
            
            this.mapObj.displayRecordedTrack();

            if (plot_id) {
                this.mapObj.selectPlot(plot_id);
            }
            else {
                this.zoomToPlotsExtent();
            }
        },
        getLocation: function() {

            var self = this;
            // onSuccess Callback
            // This method accepts a Position object, which contains the
            // current GPS coordinates
            //
            var onSuccess = function (position) {
                var currentPosition = new OpenLayers.Geometry.Point(position.coords.longitude, position.coords.latitude).transform('EPSG:4326', 'EPSG:3857');
                var inside = false;
                var gridFeatures = self.mapObj.olMap.getLayersByName('sample_grid_vector_layer')[0].features;

                for (var i = 0; i < gridFeatures.length; i++) {
                    if(gridFeatures[i].geometry.containsPoint(currentPosition)) {
                        inside = true;
                        localStorage.setItem('sampleCellId' + self.user_id, JSON.stringify({id: gridFeatures[i].id, label: gridFeatures[i].attributes.label}));
                        break;
                    }
                }
                //remove after
                if(inside === true) {
                    self.isLocating = true;
                    $('#start-sample-taking').hide();
                    $('#workflow-list').show();
                    //Активира GPS следата и измерването с GPS
                    self.GPSDrawingEnabled = true;
                    self.mapObj.measureControl.GPS.startGPSMeasure();
                    self.measureByGPSWatch();
                    if (self.mapObj.measureControl.GPS.track.components.length == 0){
                        var point = new OpenLayers.Geometry.Point(position.coords.longitude, position.coords.latitude).transform('EPSG:4326', 'EPSG:3857');

                        if(self.isSampleTakingOnPause == true) {
                            self.isSampleTakingOnPause = false;
                            for (var i = 0; i < self.geoJSON.read(self.samplePausedTrack, 'Geometry').components.length; i++) {
                                self.mapObj.measureControl.GPS.track.addPoint(self.geoJSON.read(self.samplePausedTrack, 'Geometry').components[i]);
                                if(self.samplePausedAltitudes[i] != undefined) {
                                    self.trackAltitudes.push(self.samplePausedAltitudes[i]);
                                }else {
                                    self.trackAltitudes.push(0);
                                }
                            }
                            self.samplePausedAltitudes = [];
                            self.samplePausedTrack = undefined;
                        }
                        self.mapObj.measureControl.GPS.track.addPoint(point);
                        self.trackAltitudes.push(position.coords.altitude);
                    }

                    $('#toggle-sample-taking').addClass('ui-btn-c');
                    $('#toggle-sample-taking').removeClass('ui-btn-d');

                    $('#toggle-sample-taking').html(Localization.stop);

                    $('#end-sample-taking').data('action', 'pause');
                    $('#end-sample-taking').html(Localization.pause);

                    self.mapObj.showLocationMarker(position, true, self.autoZoom, self.autoPan);
                } else {
                    self.isLocating = false;
                    navigator.notification.alert(
                        Localization.sample_start_positioning_error,    // message
                        function() {},                  // callback
                        Localization.error,             // title
                        Localization.ok                 // buttonName
                    );
                    return;
                }
            };

            // onError Callback receives a PositionError object
            //
            function onError(error) {
                self.isLocating = false;
                self.pulseGPSIcon('off');

                errorMessage = Localization.location_error;
                if(error.message == 'no data in state') {
                    errorMessage = Localization.bluetooth_connection_fail;
                }
                navigator.notification.alert(
                    errorMessage,                   // message
                    function() {},                  // callback
                    Localization.error,             // title
                    Localization.ok                 // buttonName
                );
            }
            self.locationProvider.getCurrentPosition(onSuccess, onError, {enableHighAccuracy: true, timeout:60000});
        },
        onAddSampleClick: function(e) {
            this.isLocating = true;
            /** @type {MapView} */
            var self = this;
            
            var treatmentTypesWithSampleNumbers = self.task_treatment_type.map(function(treatmentType) {
                var sampleNumberEl = $(`#sample-number-${treatmentType.value}`)
                var sampleNumber = sampleNumberEl.val().trim();
                sampleNumberEl.val("");
                return {
                    ...treatmentType,
                    sampleNumber
                }
            });
            var areAllSampleNumbersValid = treatmentTypesWithSampleNumbers.every(function(treatmentType) {
                return (SampleNumbersService.validSampleNumberLength(treatmentType.sampleNumber) &&
                !SampleNumbersService.checkSamplerNumberExists(treatmentType.sampleNumber))
            }) && !SampleNumbersService.checkDuplicateInputSamplerNumber(treatmentTypesWithSampleNumbers);

            if(!areAllSampleNumbersValid ) {
                return;
            }

            // onSuccess Callback
            // This method accepts a Position object, which contains the
            // current GPS coordinates
            //
            var onSuccess = function (position) {
                self.isLocating = false;
                var sampleCell = localStorage.getItem('sampleCellId' + self.user_id);
                sampleCell = JSON.parse(sampleCell);
                var currentPosition = new OpenLayers.Geometry.Point(position.coords.longitude, position.coords.latitude).transform('EPSG:4326', 'EPSG:3857');

                var inside = false;
                var gridFeatures = self.mapObj.olMap.getLayersByName('sample_grid_vector_layer')[0].features;
                gridFeatures = _.filter(gridFeatures, function(feature) {return feature.attributes.label == sampleCell.label && feature.geometry.containsPoint(currentPosition) });
                if(gridFeatures.length > 0) {
                    inside = true;
                }
                var areAllSamplesSet = treatmentTypesWithSampleNumbers.every(function (treatmentType) {
                    if (_.isEmpty(treatmentType.sampleNumber)) {
                        return false;
                    }
                    if(Constants.LEAF_SAMPLE_TYPE === treatmentType.value && !_.contains(self.leaf_cells, sampleCell.label.toString())) {
                        return false;
                    }
                    return true;
                });
                if(inside === false) {
                    navigator.notification.alert(
                        Localization.sample_stop_positioning_error,    // message
                        function() {},                         // callback
                        Localization.error,                    // title
                        Localization.ok                        // buttonName
                    );
                } else if(!areAllSamplesSet) {
                    navigator.notification.alert(
                        Localization.missing_sample_number,    // message
                        function() {},                         // callback
                        Localization.error,                    // title
                        Localization.ok                        // buttonName
                    );
                } else {
                    self.stopGPSMeasure();
                    var samplePoints = self.mapObj.olMap.getLayersByName('sample_points_vector_layer')[0].features;
                    var selectedCellSamplePoint = self.mapObj.updateCellStylesOnCompletingSample(sampleCell.label);

                    // save info in local storage
                    var task_id = localStorage.getItem('viewed_task_id' + self.user_id);
                    var plot_id = localStorage.getItem('viewed_plot_id' + self.user_id);

                    sampledTAsksData = JSON.parse(localStorage.getItem('sampledTasks' + self.user_id)) || {};
                    if(!sampledTAsksData.hasOwnProperty(task_id)){
                        sampledTAsksData[task_id] = {};
                    }
                    if(!sampledTAsksData[task_id].hasOwnProperty(plot_id)){
                        sampledTAsksData[task_id][plot_id] = {};
                    }

                    var samples = treatmentTypesWithSampleNumbers.map(function(treatmentType) {
                        SampleNumbersService.addSampleNumber(treatmentType.sampleNumber);
                        return {
                            sample_number: treatmentType.sampleNumber,
                            treatment_type: treatmentType.value,
                            images: treatmentType.value === Constants.LEAF_SAMPLE_TYPE ? self.tmpSampleImages : []
                        }
                    });

                    self.addTrackForSampleCellToLocalStorage();
                    track = JSON.parse(localStorage.getItem(self.user_id + '_sampleTracks'));
                    cellTrack = JSON.parse(track[task_id][plot_id][sampleCell.label]);
                    cellTrack.crs = {
                                "type":"Spherical Mercator",
                                "properties":{"name":"EPSG:3857"}
                            };
                    var cellTrack3d = JSON.parse(track[task_id][plot_id][sampleCell.label]);
                    cellTrack3d.crs = {
                                "type":"Spherical Mercator",
                                "properties":{"name":"EPSG:3857"}
                            };
                    for (var i = 0; i < cellTrack3d.coordinates.length; i++) {
                        cellTrack3d.coordinates[i].push(self.trackAltitudes[i]);
                    }

                    self.trackAltitudes = [];

                    sampledTAsksData[task_id][plot_id][sampleCell.label] = {
                        'samples': samples,
                        'timestamp': new Date(),
                        'gid': selectedCellSamplePoint.attributes.gid,
                        'track': {
                            'd2': cellTrack,
                            'd3': cellTrack3d,
                        }
                    };
                    localStorage.setItem('sampledTasks' + self.user_id, JSON.stringify(sampledTAsksData));

                    if (Object.getOwnPropertyNames(sampledTAsksData[task_id][plot_id]).length == samplePoints.length) {
                        var finishedPlots = JSON.parse(localStorage.getItem('finishedPlots' + self.user_id)) || [];
                        if (finishedPlots.indexOf(plot_id.toString()) == -1) {
                            finishedPlots.push(plot_id);
                            localStorage.setItem('finishedPlots' + self.user_id, JSON.stringify(finishedPlots));
                        }
                    }
                    //end save info in local storage
                    $("#popup-workflow-stop").popup('close');
                    $("#popup-workflow-stop").hide();
                    $('#toggle-sample-taking').addClass('ui-btn-d');
                    $('#toggle-sample-taking').removeClass('ui-btn-c');

                    $('#toggle-sample-taking').html(Localization.continue);

                    $('#end-sample-taking').data('action', 'end');
                    $('#end-sample-taking').html(Localization.end);

                    $("#toggle-sample-taking" ).removeClass("ui-disabled");
                    $("#end-sample-taking" ).removeClass("ui-disabled");

                    self.mapObj.displayRecordedTrack();
                }
            };

            // onError Callback receives a PositionError object
            //
            function onError(error) {
                self.isLocating = false;
                self.pulseGPSIcon('off');

                errorMessage = Localization.location_error;
                if(error.message == 'no data in state') {
                    errorMessage = Localization.bluetooth_connection_fail;
                }

                navigator.notification.alert(
                    errorMessage,    // message
                    function() {},                  // callback
                    Localization.error,             // title
                    Localization.ok                 // buttonName
                );
            }
            self.locationProvider.getCurrentPosition(onSuccess, onError, {enableHighAccuracy: true, timeout:60000});
        },

        checkPosition: function () {

            this.isLocating = true;

            var self = this;
            // onSuccess Callback
            // This method accepts a Position object, which contains the
            // current GPS coordinates
            //
            var onSuccess = function (position) {
                self.mapObj.locationAccuracy.removeAllFeatures();

                var currentPosition = new OpenLayers.Geometry.Point(position.coords.longitude, position.coords.latitude).transform('EPSG:4326', 'EPSG:3857');
                var inside = false;
                var gridFeatures = self.mapObj.olMap.getLayersByName('sample_grid_vector_layer')[0].features;

                for (var i = 0; i < gridFeatures.length; i++) {
                    if(gridFeatures[i].geometry.containsPoint(currentPosition)) {
                        inside = true;
                        localStorage.setItem('sampleCellId' + self.user_id, JSON.stringify({id: gridFeatures[i].id, label: gridFeatures[i].attributes.label}));
                        break;
                    }
                }

                if(inside === true) {
                    //добавяне на задачата към списъка снедовършени задачи
                    var task_id = localStorage.getItem('viewed_task_id' + self.user_id);
                    var unfinisedTasks = JSON.parse(localStorage.getItem('unfinishedTasks' + self.user_id)) || [];

                    if(unfinisedTasks.indexOf(task_id) === -1) {
                        unfinisedTasks.push(task_id);
                    }
                    localStorage.setItem('unfinishedTasks' + self.user_id, JSON.stringify(unfinisedTasks));

                    $('#start-sample-taking').hide();
                    $('#toggle-sample-taking').addClass('ui-btn-c');
                    $('#toggle-sample-taking').removeClass('ui-btn-d');
                    $('#end-sample-taking').data('action', 'pause');
                    $('#end-sample-taking').html(Localization.pause);
                    $('#workflow-list').show();

                    $("#toggle-sample-taking" ).removeClass("ui-disabled");
                    $("#end-sample-taking" ).removeClass("ui-disabled");

                    //Активира GPS следата и измерването с GPS
                    self.GPSDrawingEnabled = true;
                    self.mapObj.measureControl.GPS.startGPSMeasure();
                    self.measureByGPSWatch();
                    self.mapObj.showLocationMarker(position, true, self.autoZoom, self.autoPan);
                } else {
                    navigator.notification.alert(
                        Localization.sample_start_positioning_error,    // message
                        function() {},                  // callback
                        Localization.error,             // title
                        Localization.ok                 // buttonName
                    );
                    return;
                }
            };

            // onError Callback receives a PositionError object
            //
            function onError(error) {
                self.isLocating = false;
                self.pulseGPSIcon('off');

                errorMessage = Localization.location_error;
                if(error.message == 'no data in state') {
                    errorMessage = Localization.bluetooth_connection_fail;
                }
                navigator.notification.alert(
                    errorMessage,                   // message
                    function() {},                  // callback
                    Localization.error,             // title
                    Localization.ok                 // buttonName
                );
            }

            self.locationProvider.getCurrentPosition(onSuccess, onError, {enableHighAccuracy: true, timeout:60000});
        },
        toggleSampleTaking : function(){
            var self = this;
            if ($('#toggle-sample-taking').hasClass('ui-btn-c')) {

                $('#shallow-sample-number').val('');
                $('#sample-images').empty();
                self.tmpSampleImages = [];
                if (self.isDeepSoilSample == true) {
                    $('#deep-sample-number').val('');
                }
                if(self.isLeafSample == true) {
                    $('#leaf-sample-number').val('');
                }
                $("#popup-workflow-stop").show();
                $("#popup-workflow-stop").popup('open');

                $("#toggle-sample-taking" ).addClass("ui-disabled");
                $("#end-sample-taking" ).addClass("ui-disabled");
            } else {
                if (this.isSampleTakingOnPause === true) {
                    this.getLocation();
                }else{
                    this.getLocation();
                }
            }
        },
        pauseStopSampleTaking: function() {

            if($('#end-sample-taking').data('action') === 'pause') {

                this.isSampleTakingOnPause = true;
                this.samplePausedTrack = this.geoJSON.write(this.mapObj.measureControl.GPS.track);

                this.samplePausedAltitudes = this.trackAltitudes;
                this.trackAltitudes = [];

                this.isLocating = false;
                this.stopGPSMeasure(false, true);
                $('#end-sample-taking').data('action', 'end');
                $('#end-sample-taking').html(Localization.end);

                $('#toggle-sample-taking').addClass('ui-btn-d');
                $('#toggle-sample-taking').removeClass('ui-btn-c');

                $('#toggle-sample-taking').html(Localization.continue);

            } else if($('#end-sample-taking').data('action') === 'end') {
                $("#radio-choice-unfinished" ).prop( "checked", true ).checkboxradio( "refresh" );
                $("#radio-choice-finished" ).prop( "checked", false ).checkboxradio( "refresh" );
                $("#endSampleTakingPopup").popup("open");
                $("#endSampleTakingPopup").show();
            }
        },
        endSampleTaking: function() {
            $("#endSampleTakingPopup").popup('close');
            $("#endSampleTakingPopup").hide();
            if ($("#radio-choice-finished").is(':checked')) {
                var finishedPlots = JSON.parse(localStorage.getItem('finishedPlots' + this.user_id)) || [];

                if (finishedPlots.indexOf(this.plot_id) == -1) {
                    finishedPlots.push(this.plot_id);
                    localStorage.setItem('finishedPlots' + this.user_id, JSON.stringify(finishedPlots));
                }
                window.location.href='#unfinishedTasks';
            }
        },
        addTrackForSampleCellToLocalStorage: function () {
            var self = this;
            //Временно деактивира GPS следата и измерването с GPS
            self.GPSDrawingEnabled = false;

            var geojs = self.geoJSON.write(self.mapObj.measureControl.GPS.track),
                sampleCell = localStorage.getItem('sampleCellId' + this.user_id),
                sampleTracks = localStorage.getItem(self.user_id + '_sampleTracks');
            sampleCell = JSON.parse(sampleCell);
            sampleTracks = JSON.parse(sampleTracks);

            var parcel = localStorage.getItem('viewed_plot_id' + this.user_id);
            var order = localStorage.getItem('viewed_task_id' + this.user_id);
            if (!sampleTracks) {
                sampleTracks = {};
            }
            geojs.crs = {
                "type":"Spherical Mercator",
                "properties":{"name":"EPSG:3857"}
            };
            sampleTracks[order] = {};
            sampleTracks[order][parcel] = {};
            sampleTracks[order][parcel][sampleCell.label] = geojs;

            localStorage.setItem(self.user_id + '_sampleTracks', JSON.stringify(sampleTracks));

        },
        getTaskPlotInformationFromLocalStorage: function (task_id, plot_id) {
            var self = this;
            tasks_data = localStorage.getItem('tasks_data_' + this.user_id);
            tasks_data = JSON.parse(tasks_data);

            var current_task = _.filter(tasks_data, function(data){ return data.id = task_id; });
            return _.filter( current_task[0].plots, function(data){ return data.gid = plot_id; });

        },
        showInfo: function() {
            $('#plotInfo').show();
            $('#plotInfo').popup('open');
        },
        newLeafPin:function(leafPinParams){
//izvikva se sled popalvane na title i comment
// pri kachvane da dannite trqbva da se obarnat isNew da stane false i isSynced = true
            var self = this;
            var onSuccess = function (position) {
                // Note! This method 'toISOString()' will return the date and time according to the UTC timezone.
                // For that timezone offset could be taken with 'new Date().getTime() - new Date().getTimezoneOffset()*60*1000'
                var date = new Date(new Date().getTime() - new Date().getTimezoneOffset()*60*1000).toISOString().substring(0,19).replace('T', ' ');
                var lonlat = new OpenLayers.LonLat(position.coords.longitude, position.coords.latitude).transform('EPSG:4326', 'EPSG:3857');
                plot_id = parseInt(self.plot_id);
                
                var pin = {
                    'lon': lonlat.lon,
                    'lat': lonlat.lat,
                    'sopr_id': _.where(self.plotsData, {plot_gid: plot_id})[0].sopr_id,

                    'comment': leafPinParams.comment,
                    'title': leafPinParams.title,
                    'is_my_pin': true,
                    'date': date,

                    'isNew': true,
                    'isSynced': false
                };
                var pinsData = JSON.parse(localStorage.getItem('pins_data_' + localStorage.getItem('user_id'))) ?? [];
                self.mapObj.addNewLeafPin(pin);
                pinsData.push(pin);

                localStorage.setItem('pins_data_' + localStorage.getItem('user_id'), JSON.stringify(pinsData));
                Backbone.trigger('gs:closeNewLeafPinPopup', leafPinParams);
            };

            function onError(error) {

                errorMessage = Localization.location_error;
                if(error.message == 'no data in state') {
                    errorMessage = Localization.bluetooth_connection_fail;
                }
                navigator.notification.alert(
                    errorMessage,    // message
                    function() {},                  // callback
                    Localization.error,             // title
                    Localization.ok                 // buttonName
                );
            }

            this.locationProvider.getCurrentPosition(onSuccess, onError, {enableHighAccuracy: true, timeout:60000});
        },
        //compass functions
        initHeadingNavigation: function() {
            if(window.watchPositionID != undefined) {
                this.clearWatch();
            }
            this.watchPosition();
            this.watchCompass();
        },
        watchCompass: function(){
            var self = this;
            function onCompassUpdate(heading){
                self.currentHeading = heading.magneticHeading;
                self.updateHeading();
            }

            function onCompassError(heading){
                console.log("Compass error: " + error.code);
            }

            this.watchId = navigator.compass.watchHeading(onCompassUpdate, onCompassError, {
                frequency: 1000 // Update interval in ms
            });
        },
        updateDestination: function (lonlat){
            this.destinationPosition = new OpenLayers.LonLat( lonlat.lon, lonlat.lat).transform('EPSG:3857','EPSG:4326');
            this.updatePositions();
        },
        updatePositions: function(){
            if(!this.currentPosition || !this.destinationPosition) return;

            this.destinationBearing = OpenLayers.Spherical.computeHeading(this.currentPosition, this.destinationPosition);
            this.destinationDistance =  OpenLayers.Spherical.computeDistanceBetween(this.currentPosition, this.destinationPosition);
        },

        updateHeading: function(){
            var diff = this.destinationBearing;
            var rotation = 360 - this.currentHeading - diff;
            rotation = rotation - window.orientation;

            var rotateDeg = 'rotate(' + rotation + 'deg)';
            if(this.destinationDistance !== undefined) {
                $('#direction-heading-distance').html(this.destinationDistance.toPrecision(6) + ' м');
            }
            $('#arrow').css('-webkit-transform', rotateDeg);

            return false;
        },
        setHeading:function(){
            this.pinTool();
        },
//end compass
//barcode
        scanBarcode: function(e){
            var treatmentType = e.currentTarget.dataset.treatment;
            var resultContainer = $(`#sample-number-${treatmentType}`);

            cordova.plugins.barcodeScanner.scan(
               function (result) {
                    if(!result.cancelled){
                        // In this case we only want to process CODE_128 Codes
                       if(result.format === "CODE_128"){
                            var value = result.text;
                            resultContainer.val(value);
                       }else{
                          alert("Грешка при сканиране на баркода!");
                       }
                    }
                },
                function (error) {
                    console.log("Barcode scannig error: " + error);
                }
            );
        },
//end barcode
//images
        initPhotoControls: function(){
            var self = this;

            $('#take-photo').off().on('click', function(e) {
                TakePhoto.fromCamera(function(path, name) {
                    $('#sample-images').append('<div class="img-wrap"><span class="close">&times;</span><img name="'+ name +'" src="'+ path +'thumb_'+ name +'" onload="$(\'#loading-popup\').popup(\'close\');$.mobile.loading(\'hide\')" /></div>');

                    self.tmpSampleImages.push({
                        name: name,
                        filePath: path
                    });
                    $('.ui-popup-screen').remove();
                });
            });

            $('#choose-photo').off().on('click', function(e) {
                TakePhoto.fromGalery(function(path, name) {
                    $('#sample-images').append('<div class="img-wrap"><span class="close">&times;</span><img name="'+ name +'" src="'+ path +'thumb_'+ name +'" onload="$(\'#loading-popup\').popup(\'close\');$.mobile.loading(\'hide\');" /></div>');

                    self.tmpSampleImages.push({
                        name: name,
                        filePath: path
                    });
                });
            });

            $('#sample-images').off().on('click', '.img-wrap .close', function(e) {
                var name = $(this).closest('.img-wrap').find('img')[0].name;

                self.tmpSampleImages = $.grep(self.tmpSampleImages, function(image) {
                    return image.name !== name;
                });
                $(e.currentTarget).parent().remove();
            });
        },
// end images
        initLocationProvider: function() {
            var self = this;
            $('#location-provider input[type=radio]').on('change', function () {
                var val = 'false';
                var currentVal = localStorage.getItem('usingExternalAntenna');
                if ($("input:radio[name ='location-provider-type']:checked").val() == 'on') {
                    val = 'true';
                    if(currentVal != val) {
                        self.locationProvider = ExternalLocationProvider;
                        bluetoothSerial.isConnected(
                            function(){console.log('already connected');},
                            function(){console.log('not connected');
                            self.locationProvider.connectToDevice();
                        });
                    }
                }else if ($("input:radio[name ='location-provider-type']:checked").val() == 'off'){
                     if(currentVal != val) {
                        self.locationProvider = navigator.geolocation;
                     }
                }
                localStorage.setItem('usingExternalAntenna', val);
            });

            var usingExternal = localStorage.getItem('usingExternalAntenna');
            if(usingExternal == 'false') {
                self.locationProvider = navigator.geolocation;
            }else {
                self.locationProvider = ExternalLocationProvider;
                bluetoothSerial.isConnected(
                function(){console.log('already connected');},
                function(){console.log('not connected');
                    self.locationProvider.connectToDevice();
                });
            }
        },
        setRoadTo: function() {
            var tmpbounds =  this.mapObj.olMap.getLayersByName('sample_points_vector_layer')[0].features[0].geometry.bounds;
            var lonlat = tmpbounds.getCenterLonLat();
            var point = new OpenLayers.Geometry.Point(lonlat.lon, lonlat.lat).transform('EPSG:3857', 'EPSG:4326');

            $('#google-maps-route-btn')[0].setAttribute('href', "http://maps.google.com/?q=" + point.y + "N," + point.x + "E");
        },
        initDisplayMarkerOptions: function() {
            var self = this;

            $('#checkbox-pan').on('change', function () {
                if ($("input:checkbox[name ='checkbox-pan']:checked").val() == 'on') {
                    val = 'true';
                    self.autoPan = true;
                }else {
                    val = 'false';
                    self.autoPan = false;
                }
                localStorage.setItem('autoPan', val);
            });

            $('#checkbox-zoom').on('change', function () {
                if ($("input:checkbox[name ='checkbox-zoom']:checked").val() == 'on') {
                    val = 'true';
                    self.autoZoom = true;
                    $("#checkbox-pan").prop( "checked", true ).checkboxradio( "refresh" );
                    $("#checkbox-pan").checkboxradio( "disable" );
                    localStorage.setItem('autoZoom', val);
                }else {
                    val = 'false';
                    self.autoZoom = false;
                    $("#checkbox-pan").checkboxradio( "enable" );
                }
                localStorage.setItem('autoZoom', val);
            });

            if(localStorage.getItem('autoZoom') == 'false') {
                self.autoZoom = false;
            } else {
                self.autoZoom = true;
            }

            if(localStorage.getItem('autoPan') == 'false') {
                self.autoPan = false;
            } else {
                self.autoPan = true;
            }
        },
    });

    return new MapView();
});

define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'text!templates/login/loginTemplate.html',
    'models/SessionModel'
], function ($, _, Backbone, Localization, loginTemplate, SessionModel) {

    var LoginView = Backbone.View.extend({
        events: {
            'click #login_button': 'loginButtonClick',
        },
        el: $("#page"),
        loginBtn: '#login_button',
        initialize: function () {
            this.undelegateEvents();
        },
        render: function () {
            $('#menu').hide();
            
            var locale = (localStorage.getItem('locale')) ? localStorage.getItem('locale') : Constants.default_launguage;
            var data = {
                Localization: Localization,
                languages: Constants.languages,
                locale: locale
            };
            if (typeof loginTemplate == 'string') {
                loginTemplate = _.template(loginTemplate);
            }
            this.$el.html(loginTemplate(data)).trigger('create');
            
            $('#change-locale input[type=radio]').on('click', function () {
                $.mobile.loading('show');
                
                localStorage.setItem('locale', $(this).val());
                location.assign('');
                
                ga('send', 'event', 'locale', $(this).val());
            });
        },
        loginButtonClick: function (event) {
            var this_obj = this;
            event.preventDefault();
            $.mobile.loading('show', {
                theme: 'c',
                html: ""
            });
            $("#log").hide();
            var login_btn = $(this);

            var uname = $("#uname").val(); // get username from input
            var pass = $("#pass").val(); // get password from input
            server = Constants.servers['geoscan'].name;// get server from config

            if (!uname) { // if username is empty - print message to log
                $.mobile.loading('hide');
                $('#msg_popup #msg_content').text(Localization.enter_user);
                $('#msg_popup h1').text(Localization.error);
                $("#msg_popup").popup('open');
                return false;
            }

            if (!pass) {
                $.mobile.loading('hide');
                $('#msg_popup #msg_content').text(Localization.enter_password);
                $('#msg_popup h1').text(Localization.error);
                $("#msg_popup").popup('open');
                return false;
            }
            
            var device_platform = null;
            if(typeof device != 'undefined') {
                device_platform = device.platform
            }
            var credentials = {
                username: uname,
                password: pass,
                server: server,
                device_key: localStorage.getItem('device_key'),
                device_platform: device_platform
            };

            var login = SessionModel.login(credentials);
            login.done(function (response) {
                localStorage.setItem('isLogged', true);

                localStorage.setItem('user_id', response.user_id);
                localStorage.setItem('ORGANIZATION_EXTENT', JSON.stringify(response.ORGANIZATION_EXTENT));

                localStorage.setItem('authData', JSON.stringify({
                    username: credentials.username,
                    password: credentials.password,
                    server: credentials.server,
                    access_token: response.access_token,
                    user_id: response.user_id,
                }));

                location.reload();
                
                ga('send', 'event', 'login', 'success');
            });
            login.fail(function (response) {
                if (response.status == 401) {
                    $('#msg_popup #msg_content').text(Localization.auth_error);
                }
                else {
                    $('#msg_popup #msg_content').text(Localization.network_error);
                }

                $.mobile.loading('hide');
                $("#msg_popup").popup('open');
                
                ga('send', 'event', 'login', 'fail');
            });

            return false;
        }
    });

    return new LoginView();

});

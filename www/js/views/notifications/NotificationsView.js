define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'text!templates/notifications/notificationsTemplate.html',
    'collections/NotificationsCollection'
], function ($, _, Backbone, Localization, notificationsTemplate, NotificationsCollection) {

    var NotificationsView = Backbone.View.extend({
        events: {
            'click #noty-list li': 'onClickNoty'
        },
        el: $("#page"),
        user_id: localStorage.getItem('user_id'),
        initialize: function () {
            this.undelegateEvents();

            Backbone.on('gs:syncNotificationsSuccess', this.onSyncNotificationsSuccess, this);
            Backbone.on('gs:syncNotificationsFail', this.onSyncNotificationsFail, this);
        },
        render: function () {
            if (typeof notificationsTemplate == 'string') {
                notificationsTemplate = _.template(notificationsTemplate);
            }
            this.$el.append(notificationsTemplate()).trigger('create').trigger('resize');

            $('h1.menu-title').text(Localization.notifications);

            this.loadNotifications();
            
            $.mobile.loading('show');
            NotificationsCollection.syncNotifications();
        },
        loadNotifications: function () {
            var notificationsData = JSON.parse(localStorage.getItem('notifications_data_' + this.user_id)) || [];
            notificationsData.reverse();

            $("#noty-list").html('');

            for (var i = 0; i < notificationsData.length; i++) {
                var notification = notificationsData[i];
                
                var dateStr = notification.date.split('.')[0];
                dateStr = dateStr.replace(/-/g, '/');
                var date = new Date(dateStr);
                                                 
                var classUnread = 'unread';
                if(notification.isRead && notification.isRead === true) {
                    classUnread = '';
                }

                $("#noty-list").append('<li id="' + (notificationsData.length - 1 - i) + '" class="' + classUnread + '">' +
                                            '<a href="#plots/noty_id/' + (notificationsData.length - 1 - i) + '">' +
                                                '<p class="ui-li-aside">' + moment(date).format('L LTS') + '</p>' + 
                                                '<br /><span style="white-space:normal;">' + notification.title + '</span>' +
                                                '<p style="white-space:normal;">' + notification.message + '</p>' +
                                            '</a>' +
                                        '</li>');
            }

            $("#noty-list").listview("refresh").trigger('create').trigger('resize');

            this.$el.trigger('resize');
        },
        onSyncNotificationsSuccess: function () {
            $.mobile.loading('hide');
            
            this.loadNotifications();
        },
        onSyncNotificationsFail: function () {
            $("#img_loading").popup('close');
            $.mobile.loading('hide');
            $('#msg_popup #msg_content').text(Localization.sync_plots_error);
            $('#msg_popup h1').text(Localization.error);
            $("#msg_popup").popup('open');
        },
        onClickNoty: function(e) {
            $(e.currentTarget).removeClass('unread');
            
            var noty_id = e.currentTarget.id;
            var user_id = localStorage.getItem('user_id');
            var notyData = JSON.parse(localStorage.getItem('notifications_data_' + this.user_id));
            notyData[noty_id].isRead = true;
            
            localStorage.setItem('notifications_data_' + user_id, JSON.stringify(notyData));
        }
    });

    return new NotificationsView();
});

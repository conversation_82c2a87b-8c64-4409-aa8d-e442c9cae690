define([
    'jquery',
    'underscore',
    'backbone',
    'base64binary'
], function ($, _, Backbone) {

    var TakePhoto = function() {
        var successFunction;
        this.fromCamera = function(successCallback) {
            successFunction = successCallback;
            navigator.camera.getPicture(onSuccess, onFail, {
                quality: 90,
                destinationType: Camera.DestinationType.FILE_URI,
                targetWidth: 2560,
                targetHeight: 1920,
                correctOrientation: false
            });
        };
        
        this.fromGalery = function(successCallback) {
            successFunction = successCallback;
            navigator.camera.getPicture(onSuccess, onFail, {
                quality: 90,
                sourceType: Camera.PictureSourceType.PHOTOLIBRARY,
                destinationType: Camera.DestinationType.FILE_URI,
                targetWidth: 2560,
                targetHeight: 1920,
                correctOrientation: false
            });
        };
        
        function onSuccess(tmpImageURI) {
            console.log(tmpImageURI);
            $("#loading-popup").popup('open');
            $.mobile.loading('show');
            
            var authData = JSON.parse(localStorage.getItem('authData'));
            var fileTransfer = new FileTransfer();
            var dateObj = new Date();
            var timestamp = dateObj.getTime();
            var imageName = timestamp.toString(36) + Math.random().toString(36).substr(2, 12);
            var imageExt = '.jpg';
            var imagePath = cordova.file.dataDirectory + authData.server + '/' + authData.user_id + '/soil_samples/';

            fileTransfer.download(
                tmpImageURI,
                imagePath + imageName + imageExt,
                function (entry) {
                    
                    resizeImage(imagePath + imageName + imageExt, 100, 100, function(dataURL) {
                        console.log(dataURL);
                        saveThumbnail(imagePath, imageName, dataURL, function() {
                            if(_.isFunction(successFunction)) {
                                successFunction(imagePath, imageName + imageExt);
                            }
                        });
                    });
                },
                function (error) {
                    console.log(error);
                }
            );
        }

        function onFail(message) {
            console.log('Failed because: ' + message);
        }
        
        function resizeImage(imgSrc, width, height, callback) {
            var myCan = document.createElement('canvas');
            var img = new Image();
            img.src = imgSrc;
            img.onload = function () {
                
                myCan.id = "myTempCanvas";
                myCan.width = Number(width);
                myCan.height = Number(height);
                
                /// step 1 - resize to 50%
                var oc = document.createElement('canvas'),
                    octx = oc.getContext('2d');

                oc.width = img.width * 0.5;
                oc.height = img.height * 0.5;
                octx.drawImage(img, 0, 0, oc.width, oc.height);

                /// step 2 - resize 50% of step 1
                octx.drawImage(oc, 0, 0, oc.width * 0.5, oc.height * 0.5);

                /// step 3, resize to final size
                var ctx = myCan.getContext("2d");
                ctx.drawImage(oc, 0, 0, oc.width * 0.5, oc.height * 0.5,
                0, 0, myCan.width, myCan.height);
                
                
                var dataURL = myCan.toDataURL("image/jpeg");

                if(callback) {
                    callback(dataURL);
                }
            };
        }
        
        function saveThumbnail(path, name, dataURL, callback) {
            window.resolveLocalFileSystemURL(path, function (dir) {
                dir.getFile('thumb_' + name + '.jpg', {create: true}, function (file) {
                    file.createWriter(function (fileWriter) {

                        fileWriter.onwriteend = function (e) {
                            if(callback) {
                                callback();
                            }
                        };

                        fileWriter.onerror = function (e) {
                            console.error('Write failed: ' + e.toString());
                        };
                        
                        var byteArray = Base64Binary.decodeArrayBuffer(dataURL.substring(dataURL.indexOf(',') + 1));

                        var blob = new Blob([byteArray], {type: 'image/jpeg'});
                        fileWriter.write(blob);

                    }, function (e) {
                        console.error(e);
                    });
                }, function (e) {
                    console.error(e);
                });
            }, function (e) {
                console.error(e);
            });
        }
    };

    return new TakePhoto();
});

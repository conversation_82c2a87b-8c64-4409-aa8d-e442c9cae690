define([
    'jquery',
    'underscore',
    'backbone',
    'collections/NotificationsCollection',
    'collections/TasksCollection',
    'jquerymobile',
    'noty'
], function ($, _, Backbone, NotificationsCollection, TasksCollection) {
    
    var PushNotifications = Backbone.View.extend({
        register: function() {
            var that = this;
            
            try 
            {
                var push = window.PushNotification.init({
                    android: {
                        senderID: "751733604932"
                    },
                    ios: {
                        alert: "true",
                        badge: "true",
                        sound: "true",
                        clearBadge: "true"
                    }
                });

                push.on('registration', function(data) {
                    // data.registrationId
                    if (data.registrationId && data.registrationId.length > 0 ) {
                        localStorage.setItem('device_key', data.registrationId);
                    }
                });

                push.on('notification', function(data) {
                    // data.message,
                    // data.title,
                    // data.count,
                    // data.sound,
                    // data.image,
                    // data.additionalData
                    if (data.additionalData && data.additionalData.foreground) {
                        that.showNoty(data);
                    }
                    else {
                        //close noty for not sync plots if its open
                        $.noty.closeAll();
                        Backbone.history.navigate('newTasks', {trigger: true});
                        TasksCollection.getNewSoilOrdesData();
                    }
                });

                push.on('error', function(e) {
                    // e.message
                    console.log('error:'+ e.message);
                });
            }
            catch(err) 
            { 
                console.log(err); 
            }
        },
        showNoty: function(data) {
            navigator.notification.beep(1);

            var title = data.title;
            var message = data.message;

            if(localStorage.getItem('page_name') === 'newTasks') {
                TasksCollection.getNewSoilOrdesData();
                return;
            }

            var n = noty({
                layout: 'top',
                theme: 'relax', // or 'relax'
                type: 'alert',
                text: '<h3>' + title + '</h3>' + message, // can be html or string
                dismissQueue: true, // If you want to use queue feature set this true
                template: '<div class="noty_message"><span class="noty_text"></span><div class="noty_close"></div></div>',
                animation: {
                    open: {height: 'toggle'}, // or Animate.css class names like: 'animated bounceInLeft'
                    close: {height: 'toggle'}, // or Animate.css class names like: 'animated bounceOutLeft'
                    easing: 'swing',
                    speed: 500 // opening & closing animation speed
                },
                timeout: 5000, // delay for closing event. Set false for sticky notifications
                force: false, // adds notification to the beginning of queue when set to true
                modal: false,
                maxVisible: 5, // you can set max visible notification for dismissQueue true option,
                killer: false, // for close all notifications before show
                closeWith: ['click'], // ['click', 'button', 'hover', 'backdrop'] // backdrop click will close all notifications
                callback: {
                    onShow: function() {},
                    afterShow: function() {},
                    onClose: function() {},
                    afterClose: function() {},
                    onCloseClick: function() {
                        Backbone.history.navigate('newTasks', {trigger: true});
                        TasksCollection.getNewSoilOrdesData();
                    }
                },
                buttons: false // an array of buttons
            });
        }
    });

    return new PushNotifications();
});

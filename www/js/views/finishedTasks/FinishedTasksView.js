define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'text!templates/finishedTasks/finishedTasksTemplate.html',
    'text!templates/finishedTasks/synchronisationListItemTemplate.html',
    'collections/TasksCollection',
    'collections/NotificationsCollection'
], function ($, _, Backbone, Localization, finishedTasksTemplate, synchronisationListItemTemplate, TasksCollection, NotificationsCollection) {

    var FinishedTasksView = Backbone.View.extend({
        events: {
        },
        el: $("#page"),
        user_id: localStorage.getItem('user_id'),
        initialize: function () {

            this.undelegateEvents();
            Backbone.on('gs:sendReportSuccess', this.onSendReportSuccess, this);
            Backbone.on('gs:sendReportFail', this.onSendReportFail, this);
            var self = this;

        },
        render: function (task_id) {

            TasksCollection.removeOldSynchedTasks();
            data = {
                'Localization': Localization,
            };

            if (typeof finishedTasksTemplate == 'string') {
                finishedTasksTemplate = _.template(finishedTasksTemplate);
            }

            this.$el.append(finishedTasksTemplate(data)).trigger('create').trigger('resize');

            $('h1.menu-title').text(Localization.finished_tasks);
            
            this.renderSynchronisations();
        },
        sendReportForDate: function(date, orderId) {
            var authData = JSON.parse(localStorage.getItem('authData'));
            navigator.notification.confirm(
                Localization.sendReportConfirm + orderId + " ( " + date + ")?",
                function (button) {

                    if (button == "2") {
                        $.mobile.loading('show');
                        $("#loading-popup").popup('open');
                        TasksCollection.sendReport({
                            headers: {
                                'Authorization': 'Bearer ' + authData.access_token,
                            },
                            data: {
                                server: authData.server,
                                sync_date: date,
                                order_id: orderId
                            },
                            type: 'POST'
                        });
                    }
                },
                "",
                [Localization.no, Localization.yes]
            );
        },
        renderSynchronisations: function() {
            var self = this;

            var user_id = localStorage.getItem('user_id');
            var synchronisations = JSON.parse(localStorage.getItem('synchedTasks' + user_id)) || [];
           
            $('#synchronisations-list').on('click', 'li', function() {
                self.sendReportForDate(this.getAttribute("date"), this.getAttribute("orderId"));
            });

            $("#synchronisations-list").html('');
            var tmpRowTemplate = _.template(synchronisationListItemTemplate);
            synchronisations.forEach(function (row, index) {
                var formattedDataRow = row;
                
                formattedDataRow.date = row.date;
                formattedDataRow.order_id = row.order_id;

                var htmlForRow = tmpRowTemplate({row: formattedDataRow, Localization: Localization});
                $("#synchronisations-list").append(htmlForRow);
            });

            $("#synchronisations-list").listview("refresh").trigger('create').trigger('resize');
        },
        onSendReportFail: function() {
            $.mobile.loading('hide');
            $("#loading-popup").popup('close');
            $('#msg_popup #msg_content').text(Localization.sentReportFail);
            $('#msg_popup h1').text(Localization.error);
            $("#msg_popup").popup('open');
        },
        onSendReportSuccess: function() {
            $.mobile.loading('hide');
            $("#loading-popup").popup('close');
            $('#msg_popup #msg_content').text(Localization.successfullySentReport);
            $('#msg_popup h1').text("");
            $("#msg_popup").popup('open');
        },
    });

    return new FinishedTasksView();
});

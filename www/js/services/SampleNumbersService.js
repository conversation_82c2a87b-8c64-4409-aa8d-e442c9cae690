define(["backbone", "i18n!nls/localization"], function(Backbone, Localization) {
    var SampleNumbersService = Backbone.Model.extend({
        checkSamplerNumberExists: function(sampleNumber) {
            var storageSampleNumbers = this.getSampleNumbers();
            if (storageSampleNumbers.indexOf(sampleNumber) !== -1) {
                navigator.notification.alert(
                    Localization.duplicate_sample_number, // message
                    function() {}, // callback
                    Localization.error, // title
                    Localization.ok // buttonName
                );
                return true;
            }
            return false;
        },

        checkDuplicateInputSamplerNumber: function(treatmentTypesWithSampleNumbers) {
            var areAllSampleNumbersDifferent = treatmentTypesWithSampleNumbers.every(function(treatmentType, index, array) { 
                return array.findIndex(function(item) {
                        return item.sampleNumber === treatmentType.sampleNumber; 
                    }) === index; 
            });

            if(!areAllSampleNumbersDifferent) {
                navigator.notification.alert(
                    Localization.duplicate_sample_number, // message
                    function() {}, // callback
                    Localization.error, // title
                    Localization.ok // buttonName
                );
                return true;
            }

            return false;
        },

        validSampleNumberLength: function(value) {
            if (value.length > 7 || value.length < 7) {
                navigator.notification.alert(
                    Localization.required_sample_number_length, // message
                    function() {}, // callback
                    Localization.error, // title
                    Localization.ok // buttonName
                );
                return false;
            }
            return true;
        },

        addSampleNumber: function(sampleNumber) {
            var storageSampleNumbers = this.getSampleNumbers();

            storageSampleNumbers.push(sampleNumber);
            localStorage.setItem(
                "sample-numbers",
                JSON.stringify(storageSampleNumbers)
            );
        },

        getSampleNumbers: function() {
            return localStorage.getItem("sample-numbers") !== null
                ? JSON.parse(localStorage.getItem("sample-numbers"))
                : [];
        }
    });

    return new SampleNumbersService();
});

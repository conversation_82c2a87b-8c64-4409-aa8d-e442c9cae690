define({
    map: "Map ",
    plots: "Fields",
    new_images: "New Images",
    contacts: "Contacts",
    exit: "Exit",
    login: "Login",
    maptype: "Map Type",
    error: "Error",
    enter_user: "Enter Username",
    enter_password: "Enter Password",
    contacts_phone: "Contact Phone",
    contacts_connect: "CONTACT US",
    choose_server: "Choose Server",
    server_login: "Login",
    server_login2: "Login2",
    server_login3: "Login3",
    server_techno: "Techno",
    auth_error: "Wrong username or password.",
    network_error: "Please check you network connection.",
    sync: "Sync",
    update: "Update",
    search: "Search",
    sync_plots_error: "Sync failed!",
    confirm_exit: "Are you sure you want to leave?",
    notifications: "Notifications",
    plots_sync_advice: "There are not synchronized fields. To sync fields choose menu from upper left corner.",
    no_name: "No name",
    choose_date: "Choose date",
    by_date: "by date",
    yes: "Yes",
    no: "No",
    location_tracking: "Location Tracking",
    location_tracking_info: "Location Tracking will track and display your position on the map. Do you want to enable it?",
    measure_type: "Measure Type",
    measure_type_info: "Manual measure allows you to draw feature but GPS measure will draw feature with position change.",
    manual: "Manual",
    gps: "GPS",
    pin: "Pin",
    pins: "Pins",
    add_pin: "Adding pin",
    add_pin_info: "Manual adding allows you to tap on map but GPS adding gets your positon.",
    delete: "Delete",
    delete_confirm: "Are you sure you want to continue?",
    language: "Language",
    'bg-bg': "Български",
    'ro-ro': "Romana",
    'en-us': "English",
    'it-it': "Italiano",
    'ua-ua': "Ukrainian",
    'bg-bg-short': "BG",
    'ro-ro-short': "RO",
    'en-us-short': "EN",
    'it-it-short': "IT",
    'ua-ua-short': "UA",
    cancel: "Cancel",
    save: "Save",
    camera: "Camera",
    gallery: "Gallery",
    images: "Images",
    data: "Data",
    name: "Name",
    information: "Information",
    adding: "Adding",
    editing: "Editing",
    back: "Back",
    edit: "Edit",
    last_sync: "Last synchronization on",
    date: "Date",
    on: "On",
    off: "Off",
    username: "Username",
    password: "Password",
    location_error: "We couldn't get your location. Please make sure that GPS is enabled.",
    ordered_plots_layer: "Layer Ordered Plots",
    relative: "relative",
    absolute: "absolute",
    satellite_imaging: "Satellite Imaging",
    soil_samples: "Soil Samples",
    yr: "yr.",
    dka: "dka",
    ha: "ha",
    backup: "Backup",
    start: "Start",
    stop: "Stop",
    tasks: "Tasks",
    new_tasks: "New tasks",
    unfinished_tasks: "Unfinished tasks",
    finished_tasks: "Finished Tasks",
    no_tasks: "No tasks in the list",
    sync_tasks_error: "Unsuccessfull sync!",
    download_tasks_data: "Download data for new tasks",
    continue: "Continue",
    end: "Finish",
    finished_sampling: "The block is ready. All cells have been processed.",
    unfinished_sampling: "The block is not ready. There are still cells to be processed.",
    sample: "Sample",
    end_sampling: "End sampling",
    scan_barcode:"Scan barcode",
    set_number: "Numeration",
    ok: "ОК",
    comment: "Comment",
    sample_type: "Sample type",
    sample_depth: "Depth",
    ekatte: "EKATTE",
    sample_start_positioning_error: "Please position in the cell where you want to take sample.",
    sample_stop_positioning_error: "Sampling should begin and end in the same cell.",
    missing_sample_number: "Missing sample number.",
    duplicate_sample_number: "The sample number already exists.",
    required_sample_number_length: "The sample number must be 7 digits",
    sample_number: "Sample number",
    upload_images: "Upload images:",
    no_completely_sampled_plots: "There are no fully processed blocks.",
    no_new_tasks: "You have no new assignments.",
    new_tasks_data_successfully_retrieved: "Information about your new tasks was successfully downloaded on the device.",
    client_name: "Client name",
    plot_name: "Plot name",
    useExternalAntenna: "Use external antenna",
    bluetooth_no_paired_devices: "No connected devices via bluetooth!",
    bluetooth_connection_to_paired: "Taking data from the external device named: ",
    bluetooth_connection_success: "Successfull data retrieving from the external device.",
    bluetooth_connection_fail: "Unsuccessfull data retrieving from the external device.",
    road_to_plot:"Navigate",
    no_map_pad: "No background",
    zoom: "Zoom to position",
    pan: "Pan to position",
    show_position: "Show position:",
    treatment_types : [{
            value: 0,
            name: 'Samples 0-30 cm.',
            short_name: '0-30',
        }, {
            value: 1,
            name: 'Samples 30-60 cm.',
            short_name: '30-60',
        }, {
            value: 2,
            name: 'Leaf samples',
            short_name: 'Leaf',
        }],

    cell_count: 'Number of cells',
    start_work: 'Start work',
    pause: 'Pause',
    fileSaved: 'Saved file ',
    missing_pin_title: 'Missing pin title',
    sync_pins_error: "Sync pins failed!",
    synchronizing_samples: "Synchronizing samples.",
    synchronizing_pins: "Synchronizing pins.",
    sendReport: "Send report for task",
    successfullySentReport: "Successfully sent report!",
    sentReportFail: "Report sending failed!",
    sendReportConfirm: "Are you sure you want to send a report for task ",
    cells: 'Cells',
    forDemoSampling: 'DEMO',
    forDemoSamplingShort: 'For demo',
    type: 'Type',
    ekatte: 'EKATTE',
    plotArea: 'Area',
    dateAssigned: 'Assigned',
});
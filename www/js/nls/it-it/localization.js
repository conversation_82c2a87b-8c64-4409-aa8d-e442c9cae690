define({
    map: "Mappa",
    plots: "Campi",
    new_images: "Nuove Immagini",
    contacts: "<PERSON><PERSON><PERSON>",
    exit: "<PERSON>sci",
    login: "Accedi",
    maptype: "Tip<PERSON> di Mappa",
    error: "Errore",
    enter_user: "Inserisci Nome Utente",
    enter_password: "Inserisci Password",
    contacts_phone: "Contatto Telefonico",
    contacts_connect: "CONTATTACI",
    choose_server: "Seleziona un Server",
    server_login: "Login",
    server_login2: "Login2",
    server_login3: "Login3",
    server_techno: "Techno",
    auth_error: "Nome Utente o password non corretti.",
    network_error: "Per favore controlla la tua connessione di rete.",
    sync: "Sincronizza",
    update: "Aggiorna",
    search: "Cerca",
    sync_plots_error: "Sincronizzazione fallita!",
    confirm_exit: "Sei sicuro di voler uscire?",
    notifications: "Notifiche",
    plots_sync_advice: "Non ci sono campi sincronizzati. Per sincronizzarli scegli il menu dall'angolo in alto a sinistra.",
    no_name: "Senza nome",
    choose_date: "Scegli una data",
    by_date: "per data",
    yes: "Si",
    no: "No",
    location_tracking: "Tracciamento Posizione",
    location_tracking_info: "Tracciamento Posizione traccierà e visualizzerà la tua posizione sulla mappa. Vuoi abilitarla?",
    measure_type: "Tipi di Misura",
    measure_type_info: "Misura manuale permette di disegnare tracciati statici mentre misura GPS tracciati che possono muoversi.",
    manual: "Manuale",
    gps: "GPS",
    pin: "Puntina",
    pins: "Puntine",
    add_pin: "Aggiungi puntina",
    add_pin_info: "L'aggiunta di una puntina di tipo manuale permette di selezionare un punto nella mappa mentre l'aggiunta di un puntina GPS otterrà la tua posizione.",
    delete: "Cancella",
    delete_confirm: "Sei sicuro di voler continuare?",
    language: "Lingua",
    'bg-bg': "Bulgaro",
    'ro-ro': "Rumeno",
    'en-us': "Inglese",
    'it-it': "Italiano",
    'ua-ua': "Ucraino",
    'bg-bg-short': "BG",
    'ro-ro-short': "RO",
    'en-us-short': "EN",
    'it-it-short': "IT",
    'ua-ua-short': "UA",
    cancel: "Cancella",
    save: "Salva",
    camera: "Camera",
    gallery: "Galleria",
    images: "Immagini",
    data: "Data",
    name: "Nome",
    information: "Informazioni",
    adding: "Aggiungi",
    editing: "Modifica",
    back: "Indietro",
    edit: "Modifica",
    last_sync: "Ultima sincronizzazione il",
    date: "Date",
    on: "Acceso",
    off: "Spento",
    username: "Nome Utente",
    password: "Password",
    location_error: "Non siamo riusciti a determinare la tua posizione. Per favore controlla se il GPS è abilitato.",
    ordered_plots_layer: "Livello Ordine Campi",
    relative: "relativo",
    absolute: "assoluto",
    satellite_imaging: "Immagini Satellitari",
    soil_samples: "Campioni del terreno",
    yr: "a.",
    dka: "Da",
    ha: "ha",
    backup: "Backup",
    start: "Avvia",
    stop: "Ferma",
    tasks: "Attività",
    new_tasks: "Nuova attività",
    unfinished_tasks: "Attività non completate",
    finished_tasks: "Attività completate",
    no_tasks: "Non ci sono attività nella lista",
    sync_tasks_error: "Sincronizzazione fallita!",
    end: "Finito",
    finished_sampling: "Il blocco è pronto. Tutte le celle sono state processate.",
    unfinished_sampling: "Il blocco non è pronto. Ci sono ancora delle celle da processare.",
    sample: "Campione",
    end_sampling: "Fine Campionamento",
    scan_barcode: "Scansiona codice a barre",
    set_number: "Numerazione",
    ok: "ОК",
    continue: "Continua",
    comment: "Commento",
    sample_type: "Tipo di campione",
    sync_plots: "Campi",
    ekatte: "Identificativo",
    download_tasks_data: "Scarica dati per nuove attività",
    sample_start_positioning_error: "Per favore scegli una cella da dove prendere il campione",
    sample_stop_positioning_error: "Il campionamento dovrebbe iniziare e finire con la stessa cella.",
    missing_sample_number: "Numero campione perso.",
    duplicate_sample_number: "Il numero del campione esiste già.",
    required_sample_number_length: "Il numero del campione deve essere di 7 cifre",
    sample_number: "Numero campione",
    upload_images: "Carica immagini:",
    no_completely_sampled_plots: "Non ci sono blocchi processati completamente.",
    no_new_tasks: "Non hai nuove attività assegnate.",
    new_tasks_data_successfully_retrieved: "I dati riguardanti i tuoi nuovi compiti sono stati scaricati correttamente sul dispositivo.",
    client_name: "Nome cliente",
    plot_name: "Nome campo",
    useExternalAntenna: "Usa antenna esterna",
    bluetooth_no_paired_devices: "Non ci sono dispositivi connessi via bluetooth!",
    bluetooth_connection_to_paired: "Prendendo dati dal dispositivo chiamato: ",
    bluetooth_connection_success: "Caricamento dati da dispositivo esterno eseguito con successo.",
    bluetooth_connection_fail: "Caricamento dati da dispositivo esterno fallito.",
    road_to_plot: "Percorso per arrivarci",
    no_map_pad: "Mappa senza pad",
    zoom: "Zoom in posizione",
    pan: "Pan per posizione",
    show_position: "Mostra posizione:",
    treatment_types: [{
        value: 0,
        name: 'Campioni 0-30 cm.',
        short_name: '0-30',
    }, {
        value: 1,
        name: 'Campioni 30-60 cm.',
        short_name: '0-60',
    }, {
        value: 2,
        name: 'Campioni di foglie',
        short_name: 'foglie',
    }],
    cell_count: 'Numero di celle',
    start_work: 'Inizia il lavoro',
    pause: 'Pausa', 
    fileSaved: 'File salvato ', 
    missing_pin_title: "Nome tag mancante.",
    sync_pins_error: "Sincronizzazione tag fallita!",
    synchronizing_samples: "Sincronizzazione di esempio.",
    synchronizing_pins: "Tag sync.",
    sendReport: "Invia rapporto attività",
    successfullySentReport: "Rapporto inviato!",
    sentReportFail: "Impossibile inviare un rapporto!",
    sendReportConfirm: "Sei sicuro di voler inviare un rapporto di lavoro ",
    cells: "Celle",
    forDemoSampling: 'DEMO',
    forDemoSamplingShort: 'For demo',
    type: 'Type',
    ekatte: 'EKATTE',
    plotArea: 'Area',
    dateAssigned: 'Assigned',
});

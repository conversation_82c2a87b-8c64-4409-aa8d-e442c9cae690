define({
    map: "Карта ",
    plots: "Поля",
    new_images: "Нові зобрадження",
    contacts: "Кон<PERSON>акт<PERSON>",
    exit: "Вихід",
    login: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    maptype: "Тип карти",
    error: "Помилка",
    enter_user: "Введіть ім'я користувача",
    enter_password: "Введіть пароль",
    contacts_phone: "Контактний телефон",
    contacts_connect: "ЗВ'ЯЖІТЬСЯ З НАМИ",
    choose_server: "Виберіть Сервер",
    server_login: "<PERSON>огін",
    server_login2: "Логін2",
    server_login3: "Логін3",
    server_techno: "Техно",
    auth_error: "Неправильне ім'я користувача або пароль.",
    network_error: "Перевірте підключення до мережі.",
    sync: "Синхронізація",
    update: "Оновлення",
    search: "Пошук",
    sync_plots_error: "Помилка синхронізації!",
    confirm_exit: "Ви впевнені, що хочете вийти?",
    notifications: "Повідомлення",
    plots_sync_advice: "Немає синхронізованих полів. Для синхронізації полів виберіть меню у верхньому лівому куті.",
    no_name: "Без назви",
    choose_date: "Виберіть дату",
    by_date: "за датою",
    yes: "Так",
    no: "Ні",
    location_tracking: "Відстеження місцезнаходження",
    location_tracking_info: "відстеження місцезнаходження буде відстежувати та відображати ваше положення на карті. Ви хочете його увімкнути?",
    measure_type: "Тип виміру",
    measure_type_info: "Ручне вимірювання дозволяє намалювати об'єкт, але GPS буде точніше по ходу руху",
    manual: "Вручну",
    gps: "GPS",
    pin: "Pin-Код",
    pins: "Pin-Коди",
    add_pin: "Додавання pin-коду",
    add_pin_info: "Використання GPS зчитуватиме ваше місцезнаходження.",
    delete: "Видалити",
    delete_confirm: "Ви впевнені, що хочете продовжити?",
    language: "Мова",
    'bg-bg': "Болгарська",
    'ro-ro': "Румунська",
    'en-us': "Англійська",
    'it-it': "Італійська",
    'ua-ua': "Український",
    'bg-bg-short': "BG",
    'ro-ro-short': "RO",
    'en-us-short': "EN",
    'it-it-short': "IT",
    'ua-ua-short': "UA",
    cancel: "Скасувати",
    save: "Зберегти",
    camera: "Камера",
    gallery: "Галерея",
    images: "Зображення",
    data: "Дані",
    name: "Ім'я",
    information: "Інформація",
    adding: "Додавання",
    editing: "Редагування",
    back: "Назад",
    edit: "Редагувати",
    last_sync: "Останню синхронізацію ввімкнено",
    date: "Дані",
    on: "Увімкнено",
    off: "Вимкнено",
    username: "Ім'я користувача",
    password: "Пароль",
    location_error: "Не вдалося отримати ваше місцезнаходження. Переконайтесь, що GPS увімкнено.",
    ordered_plots_layer: "Упорядковані ділянки шару",
    relative: "відносний",
    absolute: "абсолютний",
    satellite_imaging: "Супутникові знімки",
    soil_samples: "Зразки ґрунту",
    yr: "рік.",
    dka: "dka",
    ha: "га",
    backup: "Резервне копіювання",
    start: "Старт",
    stop: "Стоп",
    tasks: "Завдання",
    new_tasks: "Нові завдання",
    unfinished_tasks: "Незакінчені завдання",
    finished_tasks: "Виконані завдання",
    no_tasks: "У списку немає завдань",
    sync_tasks_error: "Невдала синхронізація!",
    download_tasks_data: "Завантажте дані для нових завдань",
    continue: "Продовжуйте",
    end: "Готово",
    finished_sampling: "Блок готовий. Всі зразки відібрано.",
    unfinished_sampling: "Блок не готовий. Ще є ділянки для відбору.",
    sample: "Зразок",
    end_sampling: "Кінець відбору проб",
    scan_barcode:"Сканувати штрих-код",
    set_number: "Нумерація",
    ok: "ОК",
    comment: "Прокоментуйте",
    sample_type: "Тип зразка",
    sample_depth: "Глибина",
    ekatte: "EKATTE",
    sample_start_positioning_error: "Будь ласка, розташуйтесь в ділянці, де ви хочете взяти пробу.",
    sample_stop_positioning_error: "Відбір проб повинен починатися і закінчуватися в одній діялнці.",
    missing_sample_number: "Відсутній номер зразка.",
    duplicate_sample_number: "Номер зразка вже існує.",
    required_sample_number_length: "Номер вибірки повинен складати 7 цифр",
    sample_number: "Номер зразка",
    upload_images: "Завантажте зображення:",
    no_completely_sampled_plots: "відсутні повністю відібрані ділянки.",
    no_new_tasks: "У вас немає нових завдань.",
    new_tasks_data_successfully_retrieved: "Інформацію про ваші нові завдання було успішно завантажено на пристрій.",
    client_name: "Ім'я клієнта",
    plot_name: "Назва ділянки",
    useExternalAntenna: "Використовуйте зовнішню антену",
    bluetooth_no_paired_devices: "Немає підключених пристроїв через Bluetooth!",
    bluetooth_connection_to_paired: "Отримання даних із зовнішнього пристрою з іменем: ",
    bluetooth_connection_success: "Успішне отримання даних із зовнішнього пристрою.",
    bluetooth_connection_fail: "Невдале отримання даних із зовнішнього пристрою.",
    road_to_plot:"Навігація",
    no_map_pad: "Немає фону",
    zoom: "Збільшити масштаб до позиції",
    pan: "Панорамувати в положення",
    show_position: "Показати позицію:",
    treatment_types : [{
            value: 0,
            name: 'Зразки 0-30 cm.',
            short_name: '0-30',
        }, {
            value: 1,
            name: 'Зразки 30-60 cm.',
            short_name: '30-60',
        }, {
            value: 2,
            name: 'Зразки листя',
            short_name: 'Листок',
        }],

    cell_count: 'Кількість клітинок',
    start_work: 'Початок роботи',
    pause: 'Пауза',
    fileSaved: 'Збережений файл ',
    missing_pin_title: 'Відсутня назва заголовка',
    sync_pins_error: "Помилка синхронізації контактів!",
    synchronizing_samples: "Синхронізація зразків.",
    synchronizing_pins: "Синхронізуючі шпильки.",
    sendReport: "Надіслати звіт для завдання",
    successfullySentReport: "Звіт успішно надіслано!",
    sentReportFail: "Не вдалося надіслати звіт!",
    sendReportConfirm: "Ви впевнені, що хочете надіслати звіт про завдання ",
    cells: "Ділянка",
    forDemoSampling: 'DEMO',
    forDemoSamplingShort: 'For demo',
    type: 'Type',
    ekatte: 'EKATTE',
    plotArea: 'Plot area',
    dateAssigned: 'Assigned'
});
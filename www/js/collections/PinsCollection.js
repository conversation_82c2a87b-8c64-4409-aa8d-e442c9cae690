define([
    'jquery',
    'backbone',
    'i18n!nls/localization',
    'router'
], function ($, Backbone, Localization) {

    var PinsCollection = Backbone.Collection.extend({
        url: '',
        user_id: localStorage.getItem('user_id'),
        initialize: function () {
            this.refreshPinsData();
        },
        refreshPinsData: function(){
            this.pinsData = JSON.parse(localStorage.getItem('pins_data_' + this.user_id)) || [];
        },
        uploadPins: function() {
            var unsyncedPins = this.getUnsyncedPins();
            if(unsyncedPins.length === 0){
                return
            }

            $("#loading-popup").popup('open');
            $.mobile.loading("show", {
                text: Localization.synchronizing_pins,
                textVisible: true,
            });
  
            var authData = JSON.parse(localStorage.getItem('authData'));

            var self = this;
            $.ajax({
                url: Constants.servers[authData.server].ajax_url + '/api/pins/'+ this.user_id + '/create',
                headers: {
                    'Authorization': 'Bearer ' + authData.access_token,
                },
                data: {
                    server: authData.server,
                    pins: unsyncedPins
                },
                type: 'POST',
                success: function (response) {
                    $("#loading-popup").popup('close');
                    $.mobile.loading('hide');
                    self.updateSuccessfullySyncedPins();
                },
                error: function (response) {
                    if (response.status != 401 && response.statusText != 'Unauthorized') {
                        Backbone.trigger('gs:syncPinsFail');
                    }
                }
            });

        },
        setPinsData: function(pinsData){
            this.pinsData = pinsData;
            localStorage.setItem('pins_data_' + this.user_id, JSON.stringify(this.pinsData));
        },
        getAllPins: function() {
            this.refreshPinsData();
            return this.pinsData;
        },
        getUnsyncedPins: function() {
            this.refreshPinsData();
            return  unsyncedPins = _.filter(this.pinsData, function(pin){ return (pin.isNew == true && pin.isSynced == false); }); 
        },
        updateSyncedPin: function(pin) {
            pin.isNew = false;
            pin.isSynced = true;
            return pin;
        },
        updateSuccessfullySyncedPins: function() {
            _.each(this.pinsData, this.updateSyncedPin)
            this.setPinsData(this.pinsData);
        }
    });

    return new PinsCollection();
});
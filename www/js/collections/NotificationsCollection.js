define([
    'jquery',
    'backbone',
    'router'
], function ($, Backbone, Router) {

    var NotificationsCollection = Backbone.Collection.extend({
        initialize: function () {

        },
        getNotifications: function () {

        },
        syncNotifications: function () {
            var authData = JSON.parse(localStorage.getItem('authData'));
            var localOffset = (-1) * new Date().getTimezoneOffset() * 60;
            
            var notySyncDate = Math.floor(localStorage.getItem('noty_sync_' + authData.user_id) / 1000) + localOffset;

            $.ajax({
                url: Constants.servers[authData.server].ajax_url + '/api/notifications/' + authData.user_id + '/' + notySyncDate,
                headers: {
                    'Authorization': 'Bearer ' + authData.access_token,
                },
                data: {
                    server: authData.server
                },
                type: 'POST',
                success: function (response) {
                    var notificationsData = JSON.parse(localStorage.getItem('notifications_data_' + authData.user_id)) || [];
                    var notyData = notificationsData.concat(response);

                    localStorage.setItem('notifications_data_' + localStorage.getItem('user_id'), JSON.stringify(notyData));

                    Backbone.trigger('gs:syncNotificationsSuccess');
                },
                error: function () {
                    Backbone.trigger('gs:syncNotificationsFail');
                }
            });
        }
    });

    return new NotificationsCollection();
});
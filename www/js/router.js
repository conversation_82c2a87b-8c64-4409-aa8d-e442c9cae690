// Filename: router.js
define([
    'jquery',
    'underscore',
    'backbone',
    'i18n!nls/localization',
    'views/login/LoginView',
    'views/menu/MenuView',
    'views/map/MapView',
    'views/contacts/ContactsView',
    'views/widgets/PushNotifications',
    'views/tasks/TasksView',
    'views/finishedTasks/FinishedTasksView',
    'views/task_plots/TaskPlotsView',
    'views/plots_list/PlotsListView'
], function ($, _, Backbone, Localization, LoginView, MenuView, MapView, ContactsView, PushNotifications, TasksView, FinishedTasksView, TaskPlotsView, PlotsListView) {
    var AppRouter = Backbone.Router.extend({
        routes: {
            // Define some URL routes
            'map/task_id/:task_id/plot_id/:plot_id': 'showPlotOnMap',
            'contacts': 'showContacts',
            'tasks': 'showTasks',
            'newTasks': 'showNewTasks',
            'unfinishedTasks': 'showUnfinishedTasks',
            'plotsList/task_id/:task_id': 'showPlotsList',
            'finishedTasks': 'showFinishedTasks',
            'taskPlots/task_id/:task_id':'showTaskPlots',
            'tasksPlots':'showTasksPlots',
            'logout': 'logout',
            // Default
            '*actions': 'defaultAction'
        },
        showMap: function () {
            var prevPage = localStorage.getItem('page_name');
            if (prevPage == 'plotOnMap' || prevPage == 'pinOnMap') {
                return;
            }

            localStorage.setItem('page_name', 'map');

            MenuView.render();
            MapView.render();
            
            ga('send', 'screenview', {'screenName': 'map'});
        },
        showPlotOnMap: function (task_id, plot_id) {
            localStorage.setItem('page_name', 'plotOnMap');
            localStorage.setItem('viewed_plot_id' + localStorage.getItem('user_id'), plot_id);
            localStorage.setItem('viewed_task_id' + localStorage.getItem('user_id'), task_id);
            MenuView.render();
            MapView.render(task_id, plot_id);
            
            ga('send', 'screenview', {'screenName': 'plotOnMap'});
        },
        showContacts: function () {
            localStorage.setItem('page_name', 'contacts');
            MenuView.render();
            ContactsView.render();
            
            ga('send', 'screenview', {'screenName': 'contacts'});
        },
        showTasks: function () {
            localStorage.setItem('page_name', 'tasks');

            MenuView.render();
            TasksView.render();
            
            ga('send', 'screenview', {'screenName': 'tasks'});
        },
        showNewTasks: function () {
            localStorage.setItem('page_name', 'tasks');

            MenuView.render();
            TasksView.renderNewTasks();
            
            ga('send', 'screenview', {'screenName': 'tasks'});
        },
        showFinishedTasks: function () {
            localStorage.setItem('page_name', 'finished_tasks');

            MenuView.render();
            FinishedTasksView.render();
            
            ga('send', 'screenview', {'screenName': 'finished_tasks'});
        },
        showUnfinishedTasks: function() {
            localStorage.setItem('page_name', 'tasks');
            
            MenuView.render();
            TasksView.renderUnfinishedTasks();
            
            ga('send', 'screenview', {'screenName': 'tasks'});
        },
        showPlotsList: function(task_id) {
            localStorage.setItem('page_name', 'plots_list');
            
            MenuView.render();
            PlotsListView.render(task_id);
        },
        showTaskPlots: function (task_id) {
            localStorage.setItem('page_name', 'task_plots');

            MenuView.render();
            TaskPlotsView.render(task_id);
            
        },
        showTasksPlots: function () {
            localStorage.setItem('page_name', 'task_plots');

            MenuView.render();
            TaskPlotsView.renderAllPlots();
            
        },
        logout: function () {
            localStorage.setItem('isLogged', false);
            // localStorage.clear();
            Backbone.history.navigate('login', {trigger: true});

            ga('send', 'event', 'logout');
        },
        defaultAction: function () {
            localStorage.setItem('page_name', 'default');
            //If user not logged show login page, else show menu and load page related to url 
            logged_in = localStorage.getItem('isLogged');
            if (logged_in == 'true') {
                //User is logged
                Backbone.history.navigate('tasks', {trigger: true});
                
                ga('send', 'screenview', {'screenName': 'tasks'});
            }
            else {
                //User is not logged
                LoginView.render();
                
                ga('send', 'screenview', {'screenName': 'login'});
            }
        }
    });

    var initialize = function () {
        var clientId;
        if(typeof device != 'undefined') {
            clientId = device.uuid;
        }
        
        // Google Analytics setings
        ga('create', 'UA-44073592-2', {
            'storage': 'none',
            'clientId': clientId
        });

        ga('set','checkProtocolTask',null);
        ga('set','checkStorageTask',null);
        
        if (navigator.appInfo) {
            navigator.appInfo.getAppInfo(function (appInfo) {
                ga('set', {
                    'appName': 'agrobalance',
                    'appId': appInfo.identifier,
                    'appVersion': appInfo.version
                });
            }, function (err) {
                console.log(err);
            });
        }
        // End Google Analytics
        
        document.addEventListener("backbutton", function (event) {
            if(Constants.backbutton === 'disable') {
                return false;
            }
            
            $('.ui-popup').popup('close');
            
            var page = localStorage.getItem('page_name');
            if(page == 'map' || page == 'login' || page == 'default') {
                event.preventDefault();
                navigator.notification.confirm(
                        Localization.confirm_exit,
                        function (button) {
                            if (button == "2") {
                                navigator.app.exitApp();
                            }
                        },
                        Localization.exit,
                        ['Cancel', 'OK']
                        );
            }
            else {
                navigator.app.backHistory();
            }
            
        });

        document.addEventListener("menubutton", function (event) {
            event.preventDefault();
            $('#nav-settings').panel('open');
        });

        document.addEventListener("searchbutton", function (event) {
            event.preventDefault();
            Backbone.history.navigate('plots', {trigger: true});
        });

        var app_router = new AppRouter();
        
        app_router.on('route', function(route) {
            //$.mobile.loading('hide');
        });

        Backbone.history.start();

        PushNotifications.register();
    };
    return {
        initialize: initialize
    };
});

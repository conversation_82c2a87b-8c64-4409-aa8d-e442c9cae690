@font-face {
	font-family: 'nikicons';
	src:url('fonts/nikicons.eot?j8xphm');
	src:url('fonts/nikicons.eot?#iefixj8xphm') format('embedded-opentype'),
		url('fonts/nikicons.woff?j8xphm') format('woff'),
		url('fonts/nikicons.ttf?j8xphm') format('truetype'),
		url('fonts/nikicons.svg?j8xphm#nikicons') format('svg');
	font-weight: normal;
	font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
	font-family: 'nikicons';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
    vertical-align: middle;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-dot-menu:before {
	content: "\e614";
}
.icon-ruler-area:before {
	content: "\e613";
}
.icon-clear:before {
	content: "\e612";
}
.icon-zones:before {
	content: "\e611";
}
.icon-ruler:before {
	content: "\e610";
}
.icon-hand:before {
	content: "\e600";
}
.icon-tractor:before {
	content: "\e601";
}
.icon-location:before {
	content: "\e602";
}
.icon-pointer:before {
	content: "\e603";
}
.icon-trace:before {
	content: "\e604";
}
.icon-popup:before {
	content: "\e605";
}
.icon-cog:before {
	content: "\e606";
}
.icon-list:before {
	content: "\e607";
}
.icon-arrow-left:before {
	content: "\e608";
}
.icon-arrow-down:before {
	content: "\e609";
}
.icon-arrow-up:before {
	content: "\e60a";
}
.icon-arrow-right:before {
	content: "\e60b";
}
.icon-arrow-left2:before {
	content: "\e60c";
}
.icon-arrow-down2:before {
	content: "\e60d";
}
.icon-arrow-up2:before {
	content: "\e60e";
}
.icon-arrow-right2:before {
	content: "\e60f";
}

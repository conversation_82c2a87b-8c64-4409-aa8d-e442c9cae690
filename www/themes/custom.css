body.ui-mobile-viewport.map-view {overflow: hidden;}
.ui-panel-wrapper {height: 100%; overflow: hidden;}
/* Global Styles */
.text-center {text-align: center;}
h1 {font-size: 1.125em;}
.ui-page-theme-a .ui-btn:focus, html .ui-bar-a .ui-btn:focus, html .ui-body-a .ui-btn:focus, html body .ui-group-theme-a .ui-btn:focus, html head + body .ui-btn.ui-btn-a:focus, .ui-page-theme-a .ui-focus, html .ui-bar-a .ui-focus, html .ui-body-a .ui-focus, html body .ui-group-theme-a .ui-focus, html head + body .ui-btn-a.ui-focus, html head + body .ui-body-a.ui-focus {box-shadow: none;}
html .ui-btn-b {color: #353535;}
body, input, select, textarea, button, .ui-btn {font-family: 'Open Sans', sans-serif;}
.ui-page-theme-a a {font-weight: 600;}
.ui-mobile label, div.ui-controlgroup-label {font-weight: 300;}


/* Top red bar */
.ui-header.red-line {
	border-top: 4px solid #22A84C;
}

/* Current user location button */
.ui-btn.location-btn {
	position: absolute;
	top: 5em;
	right: 0.5em;
	z-index: 999;
	background-color: rgb(0, 0, 0);
    background-color: rgba(0, 0, 0, 0.65);
	color: #fff;
	border: none;
	font-size: 1.5em;
	padding: 0;
	text-shadow: none;
	height: 45px;
	width: 45px;
	line-height: 48px;
}
.ui-btn.location-btn:hover {
	background-color: rgb(0, 0, 0);
    background-color: rgba(0, 0, 0, 0.65);
    color: #fff;
    text-shadow: none;
}

/* Map Toolbar */
#ol-measure-overlay {
	color: #FFFFFF;
	text-shadow: none;
	font-size: 1.2em;
	padding: 3px 5px;
	background-color: #086928;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	display: none;
}

.map-toolbar {
	background: #eeeeee;
	padding: 0.5em;
	border-top: 1px solid #e4e4e4;
}
	/* Icon Font buttons */
	.map-toolbar .fi {
		font-size: 1.5em;
		color: #5c5c5c;
		padding: 7px;
		border: 3px solid #c5c5c5;
		border-radius: 5px;
		text-shadow: none;
		display: inline-block;
		margin-right: 0.5em;
	}
    
    .map-toolbar .fix{
        padding: 7px 9px;
    }
    
        .map-toolbar .fi:hover { 
            cursor: pointer;
        }
        .map-toolbar .fi.current, .map-toolbar .fi:active {
            border: 3px solid #22A84C;
            background: #d9d9d9;
            text-shadow: none;
        }
		
/* Main Menu Button */
	html .ui-bar-a .header-menu-btn.ui-btn {
		background: none;
		border: none;
		box-shadow: none;
		padding: 0.5em;
	}
	/* Icon Font buttons */
	.header-menu-btn .fi  {
		display: block;
		float: left;
		font-size: 2.2em;
        font-weight: 900;
		color: #22A84C;
	}

	.ui-btn.ui-btn-active .fi {
		color: #333;
		text-shadow: none;
	}
	.menu-img {
		max-height: 100%;
		display: block;
		float: left;
	}
	.menu-logo {
		background-attachment: scroll;
		background-image: url(images/ic-launcher.png);
		background-position: left 30%;
		background-repeat: no-repeat;
		-o-background-size:      100%;
		-moz-background-size:    100%;
		-webkit-background-size: 100%;
		background-size:         100%;
		display: block;
		float: left;
		font-size: 1em;
		height: 2.5em;
		width: 2.1em;
	}

/* Footer Bar Icons */
.footer-view .fi {
	font-size: 2em;
	color: #5c5c5c;
}
.footer-view .ui-btn.view-mode.current {
	background-color: #ddd;
}

/* Custom Checkbox Lists */
	.custom-list legend {
		font-weight: 600;
		background-color: #eee;
		display: block;
		padding: 0.875em;
		width: 100%;
	}
	.custom-list .check-list .ui-btn {
		background-color: #fbfbfa;
		border: none;
		padding: 0.438em 5em 0.438em 1.5em;
		font-weight: 300;
	}
		.custom-list .check-list .ui-btn:hover {
			background-color: #fbfbfa;
		}
	.custom-list .ui-btn.ui-checkbox-off:after, .custom-list .ui-btn.ui-checkbox-on:after {
		margin: -9px -9px 0;
	}
	.custom-list .list .ui-checkbox {
		padding: 0.438em 1em;
		margin: 0;
	}
	.custom-list .list > div.ui-checkbox {
		border-bottom: 1px solid #eee;
	}
	.custom-list .list > div.ui-checkbox:last-of-type {
		border: none;
	}
	.custom-list .list .ui-checkbox input {
		margin: -11px 0 0 10px;
	}
	
	.ui-btn.ui-btn-margin {
		margin: 1em;
	}
	.ui-footer .ui-btn.ui-btn-wide {
		display: block;
		font-size: 1em;
		font-weight: 600;
	}

/* Custom Forms */	
.custom-filters .ui-select .ui-btn {
	font-weight: 300;
	text-align: left;
	padding: 0.5em 1em;
}
.custom-filters .ui-select .ui-shadow {
	box-shadow: none;
}
.custom-filters  .ui-shadow-inset {
	box-shadow: none;
}

.ui-popup-container {
	
}
.ui-selectmenu.ui-overlay-shadow.ui-body-inherit {
	border-color: #ddd;
}
.ui-selectmenu.ui-popup .ui-header .ui-title {
	text-align: left;
	margin: 0;
	font-weight: 600;
	padding-left: 1em;
}
.ui-selectmenu.ui-popup ul a {
	font-weight: 300;
}
.ui-corner-all {
	border-radius: 0.375em;
}
.ui-overlay-shadow {
	box-shadow: none;
}
.custom-filters .ui-input-text input {
	border-radius: 0.375em;
}

.ui-selectmenu .ui-dialog-contain {
	margin-top: 1em;
	max-width: 900px;
}
.ui-selectmenu-list.ui-listview .ui-li-divider {
	background-color: #eee;
	padding: 0.875em 1em;	
	font-size: 1em;
}
.ui-selectmenu-list.ui-listview .ui-btn {
	font-weight: 300;
}
	
/* Custom Content page */
.ui-content.custom-list, .ui-content.custom-page {
	padding: 0;
}
.custom-page h2 {
	font-size: 1.25em;
	font-weight: 400;
	margin-left: 1em;
}
.custom-page .ui-bar {
	background-color: #eee;
	font-size: 1.125em;
	font-weight: 400;
	margin-bottom: 0;
}

.custom-page .ui-body {
	font-weight: 300;
}

.relative {
	position: relative;
}
a.right-link {
	position: absolute;
	top: 25%;
	border-radius: 99px;
	background: #ccc;
	padding: 2px;
	margin-bottom: -0.75em;
	right: 10px;
	z-index: 2;
	text-decoration: none;
	font-size: 1.438em;
	text-shadow: none;
	font-weight: 400;
	color: #fff !important;
	width: 22px;
	height: 22px;
	text-align: center;
	line-height: 23px;
}

/* Temp */

.ui-btn.machine-icon-map {
	position: absolute;
	top: 20%;
	right: 50%;
	z-index: 999;
}

/* Plots history images */

#plot-history-tabs {
    position: fixed;
    width: 100%;
    height: 70px;
    bottom: 10px;
    left: 0;
    z-index: 1000;
    display: none;
}

#plot-history-tabs .slick {
    margin-bottom: 5px;
    display: none;
}

#plot-history-tabs .slick .active {
    display: block;
}

#plot-history-tabs .slick .image-wrapper{
	background: rgba(255,255,255,.4);
    padding: 3px;
	margin: 0px 5px;
	
	text-align: center;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}

#plot-history-tabs .slick .image-wrapper .selected{
	background: #1d5d26;
}

#plot-history-tabs .slick .image-item {
	background: #22A84C;
    padding: 5px;
}

#plot-history-tabs .slick .image-item  img {
	margin: auto;
	max-width: 100px;
    max-height: 100px;
}

#plot-history-tabs .slick .image-item  span {
	color: #fff;
	text-shadow: none;
	font-size: 12px;
}

#plot-history-tabs .slick .image-item  hr {
	border: none;
	color: #FFF;
	background-color: #FFF;
	height: 1px;
}

.unread a {
    color: #22A84C !important;
}

#watchPositionInfo {
    font-weight: bold;
    font-size: 14px;
    line-height: 110%;
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #FFF;
    z-index: 1000;
    opacity: 0.7;
    display: none;
}

#watchPositionInfo button {
    width: 80px;
    float: left;
}

#watchPositionText {
    float: right;
    padding: 15px 10px;
}

.img-wrap {
    position: relative;
    display: inline-block;
    font-size: 0;
}
.img-wrap .close {
    position: absolute;
    top: 2px;
    right: 2px;
    z-index: 100;
    background-color: #FFF;
    padding: 4px 5px 3px;
    color: #000;
    font-weight: bold;
    cursor: pointer;
    opacity: .8;
    text-align: center;
    font-size: 32px;
    line-height: 20px;
    border-radius: 50%;
}

.footer {
    position: fixed;
    bottom: 0;
    left:0;
    width: 100%;
    background-color: #fbfbfa;
    border-top: 1px solid #ececec;
    z-index: 200;
}

.req {
    color: #bb0000;
}

.no-border{
    border-color: transparent !important;
}

#sample-images {
    min-height: 100px;
}

.ui-panel.ui-panel-open {
    position:fixed;
}
.ui-panel-inner {
    position: absolute;
    top: 1px;
    left: 0;
    right: 0;
    bottom: 0px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}

.ui-listview>li h4 {
	margin-bottom: 0;
}

ul .ui-body-b{
    border: 1px solid       #999;
    background:             #22A84C;
    color:                  #fff;
    text-shadow: 0 1px 0 #fff;
    font-weight: normal;
}
.ui-btn-corner-all {
    border-radius:                      .8em;
}

.btn-half {
    height: 100%;
    width: 50%;
}

#compass
{
     width: 50%;
    transform:rotate(0deg); /* in case someone creates a compliant browser */
    -ms-transform:rotate(0deg); /* WP7 */
    -webkit-transform:rotate(0deg); /* iOS and Android */
}
#compass-container{
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 1000;
    padding: 90px 70px;
}

 #arrow{
    position: absolute;
    width: 50px;
    height: 50px;
    background: 50% 50% no-repeat; 
    background-size: 50px 50px;
    background-image: url('images/arrow.png');
    top: 0;
    left: 50%;
    margin: 30px 0 0 -15px;
}

#sync-tasks {
    height: 70px !important;
}

#get-new-tasks{
    height: 70px !important;
}

#start-sample-taking {
    height: 70px !important;
    bottom: 10px;
}

#toggle-sample-taking {
    height: 60px;
}

#end-sample-taking {
    height: 60px;
}

#workflow-list {
    position: fixed;
    width: 100%;
    height: 80px;
    bottom: 0px;
    left: 0;
    z-index: 1000;
    /*display: none;*/
}

div.olControlZoom a {
    font-family: 'Lucida Grande', Verdana, Geneva, Lucida, Arial, Helvetica, sans-serif;
    font-weight: bold;
    text-decoration: none;
    text-align: center;
    height: 25px;
    width:25px;
    line-height: 19px;
    background: #22A84C !important;
    background: rgba(7, 133, 199, 1);
    filter: alpha(opacity=80);
}


/* C
-----------------------------------------------------------------------------------------------------------*/
/* Bar: Toolbars, dividers, slider track */
.ui-bar-c,
.ui-page-theme-c .ui-bar-inherit,
html .ui-bar-c .ui-bar-inherit,
html .ui-body-c .ui-bar-inherit,
html body .ui-group-theme-c .ui-bar-inherit {
    background-color: #d12e2e /*{c-bar-background-color}*/;
    border-color: #dddddd /*{c-bar-border}*/;
    color: #333333 /*{c-bar-color}*/;
    text-shadow: 0 /*{c-bar-shadow-x}*/ 1px /*{c-bar-shadow-y}*/ 0 /*{c-bar-shadow-radius}*/ #eeeeee /*{c-bar-shadow-color}*/;
    font-weight: bold;
}
.ui-bar-c {
    border-width: 1px;
    border-style: solid;
}
/* Page and overlay */
.ui-overlay-c,
.ui-page-theme-c,
.ui-page-theme-c .ui-panel-wrapper {
    background-color: #f7f7f7 /*{c-page-background-color}*/;
    border-color: #bbbbbb /*{c-page-border}*/;
    color: #333333 /*{c-page-color}*/;
    text-shadow: 0 /*{c-page-shadow-x}*/ 1px /*{c-page-shadow-y}*/ 0 /*{c-page-shadow-radius}*/ #f3f3f3 /*{c-page-shadow-color}*/;
}
/* Body: Read-only lists, text inputs, collapsible content */
.ui-body-c,
.ui-page-theme-c .ui-body-inherit,
html .ui-bar-c .ui-body-inherit,
html .ui-body-c .ui-body-inherit,
html body .ui-group-theme-c .ui-body-inherit,
html .ui-panel-page-container-c {
    background-color: #d12e2e /*{c-body-background-color}*/;
    border-color: #dddddd /*{c-body-border}*/;
    color: #e9e2e2 /*{c-body-color}*/;
    text-shadow: 0 /*{c-body-shadow-x}*/ 1px /*{c-body-shadow-y}*/ 0 /*{c-body-shadow-radius}*/ #f3f3f3 /*{c-body-shadow-color}*/;
}
.ui-body-c {
    border-width: 1px;
    border-style: solid;
}
/* Links */
.ui-page-theme-c a,
html .ui-bar-c a,
html .ui-body-c a,
html body .ui-group-theme-c a {
    color: #b9d4e9 /*{c-link-color}*/;
    font-weight: bold;
}
.ui-page-theme-c a:visited,
html .ui-bar-c a:visited,
html .ui-body-c a:visited,
html body .ui-group-theme-c a:visited {
    color: #3388cc /*{c-link-visited}*/;
}
.ui-page-theme-c a:hover,
html .ui-bar-c a:hover,
html .ui-body-c a:hover,
html body .ui-group-theme-c a:hover {
    color: #1d5d26 /*{c-link-hover}*/;
}
.ui-page-theme-c a:active,
html .ui-bar-c a:active,
html .ui-body-c a:active,
html body .ui-group-theme-c a:active {
    color: #1d5d26 /*{c-link-active}*/;
}
/* Button up */
.ui-page-theme-c .ui-btn,
html .ui-bar-c .ui-btn,
html .ui-body-c .ui-btn,
html body .ui-group-theme-c .ui-btn,
html head + body .ui-btn.ui-btn-c,
/* Button visited */
.ui-page-theme-c .ui-btn:visited,
html .ui-bar-c .ui-btn:visited,
html .ui-body-c .ui-btn:visited,
html body .ui-group-theme-c .ui-btn:visited,
html head + body .ui-btn.ui-btn-c:visited {
    background-color: #d12e2e /*{c-bup-background-color}*/;
    border-color: #dddddd /*{c-bup-border}*/;
    color: #e9e2e2 /*{c-bup-color}*/;
    text-shadow: 0 /*{c-bup-shadow-x}*/ 0px /*{c-bup-shadow-y}*/ 0 /*{c-bup-shadow-radius}*/ #f3f3f3 /*{c-bup-shadow-color}*/;
}
/* Button hover */
.ui-page-theme-c .ui-btn:hover,
html .ui-bar-c .ui-btn:hover,
html .ui-body-c .ui-btn:hover,
html body .ui-group-theme-c .ui-btn:hover,
html head + body .ui-btn.ui-btn-c:hover {
    background-color: #861d1d /*{c-bhover-background-color}*/;
    border-color: #dddddd /*{c-bhover-border}*/;
    color: #e9e2e2 /*{c-bhover-color}*/;
    text-shadow: 0 /*{c-bhover-shadow-x}*/ 0px /*{c-bhover-shadow-y}*/ 0 /*{c-bhover-shadow-radius}*/ #f3f3f3 /*{c-bhover-shadow-color}*/;
}
/* Button down */
.ui-page-theme-c .ui-btn:active,
html .ui-bar-c .ui-btn:active,
html .ui-body-c .ui-btn:active,
html body .ui-group-theme-c .ui-btn:active,
html head + body .ui-btn.ui-btn-c:active {
    background-color: #e8e8e8 /*{c-bdown-background-color}*/;
    border-color: #dddddd /*{c-bdown-border}*/;
    color: #333333 /*{c-bdown-color}*/;
    text-shadow: 0 /*{c-bdown-shadow-x}*/ 1px /*{c-bdown-shadow-y}*/ 0 /*{c-bdown-shadow-radius}*/ #f3f3f3 /*{c-bdown-shadow-color}*/;
}
/* Active button */
.ui-page-theme-c .ui-btn.ui-btn-active,
html .ui-bar-c .ui-btn.ui-btn-active,
html .ui-body-c .ui-btn.ui-btn-active,
html body .ui-group-theme-c .ui-btn.ui-btn-active,
html head + body .ui-btn.ui-btn-c.ui-btn-active,
/* Active checkbox icon */
.ui-page-theme-c .ui-checkbox-on:after,
html .ui-bar-c .ui-checkbox-on:after,
html .ui-body-c .ui-checkbox-on:after,
html body .ui-group-theme-c .ui-checkbox-on:after,
.ui-btn.ui-checkbox-on.ui-btn-c:after,
/* Active flipswitch background */
.ui-page-theme-c .ui-flipswitch-active,
html .ui-bar-c .ui-flipswitch-active,
html .ui-body-c .ui-flipswitch-active,
html body .ui-group-theme-c .ui-flipswitch-active,
html body .ui-flipswitch.ui-bar-c.ui-flipswitch-active,
/* Active slider track */
.ui-page-theme-c .ui-slider-track .ui-btn-active,
html .ui-bar-c .ui-slider-track .ui-btn-active,
html .ui-body-c .ui-slider-track .ui-btn-active,
html body .ui-group-theme-c .ui-slider-track .ui-btn-active,
html body div.ui-slider-track.ui-body-c .ui-btn-active {
    background-color: #d12e2e /*{c-active-background-color}*/;
    border-color: #1c4a70 /*{c-active-border}*/;
    color: #ffffff /*{c-active-color}*/;
    text-shadow: 0 /*{c-active-shadow-x}*/ 1px /*{c-active-shadow-y}*/ 0 /*{c-active-shadow-radius}*/ #1d5d26 /*{c-active-shadow-color}*/;
}
/* Active radio button icon */
.ui-page-theme-c .ui-radio-on:after,
html .ui-bar-c .ui-radio-on:after,
html .ui-body-c .ui-radio-on:after,
html body .ui-group-theme-c .ui-radio-on:after,
.ui-btn.ui-radio-on.ui-btn-c:after {
    border-color: #3388cc /*{c-active-background-color}*/;
}
/* Focus */
.ui-page-theme-c .ui-btn:focus,
html .ui-bar-c .ui-btn:focus,
html .ui-body-c .ui-btn:focus,
html body .ui-group-theme-c .ui-btn:focus,
html head + body .ui-btn.ui-btn-c:focus,
/* Focus buttons and text inputs with div wrap */
.ui-page-theme-c .ui-focus,
html .ui-bar-c .ui-focus,
html .ui-body-c .ui-focus,
html body .ui-group-theme-c .ui-focus,
html head + body .ui-btn-c.ui-focus,
html head + body .ui-body-c.ui-focus {
    -webkit-box-shadow: 0 0 12px #3388cc /*{c-active-background-color}*/;
    -moz-box-shadow: 0 0 12px #3388cc /*{c-active-background-color}*/;
    box-shadow: 0 0 12px #3388cc /*{c-active-background-color}*/;
}

/* D
-----------------------------------------------------------------------------------------------------------*/
/* Bar: Toolbars, dividers, slider track */
.ui-bar-d,
.ui-page-theme-d .ui-bar-inherit,
html .ui-bar-d .ui-bar-inherit,
html .ui-body-d .ui-bar-inherit,
html body .ui-group-theme-d .ui-bar-inherit {
    background-color: #e9e9e9 /*{d-bar-background-color}*/;
    border-color: #dddddd /*{d-bar-border}*/;
    color: #333333 /*{d-bar-color}*/;
    text-shadow: 0 /*{d-bar-shadow-x}*/ 1px /*{d-bar-shadow-y}*/ 0 /*{d-bar-shadow-radius}*/ #eeeeee /*{d-bar-shadow-color}*/;
    font-weight: bold;
}
.ui-bar-d {
    border-width: 1px;
    border-style: solid;
}
/* Page and overlay */
.ui-overlay-d,
.ui-page-theme-d,
.ui-page-theme-d .ui-panel-wrapper {
    background-color: #f9f9f9 /*{d-page-background-color}*/;
    border-color: #bbbbbb /*{d-page-border}*/;
    color: #333333 /*{d-page-color}*/;
    text-shadow: 0 /*{d-page-shadow-x}*/ 1px /*{d-page-shadow-y}*/ 0 /*{d-page-shadow-radius}*/ #f3f3f3 /*{d-page-shadow-color}*/;
}
/* Body: Read-only lists, text inputs, collapsible content */
.ui-body-d,
.ui-page-theme-d .ui-body-inherit,
html .ui-bar-d .ui-body-inherit,
html .ui-body-d .ui-body-inherit,
html body .ui-group-theme-d .ui-body-inherit,
html .ui-panel-page-container-d {
    background-color: #ffffff /*{d-body-background-color}*/;
    border-color: #dddddd /*{d-body-border}*/;
    color: #333333 /*{d-body-color}*/;
    text-shadow: 0 /*{d-body-shadow-x}*/ 1px /*{d-body-shadow-y}*/ 0 /*{d-body-shadow-radius}*/ #f3f3f3 /*{d-body-shadow-color}*/;
}
.ui-body-d {
    border-width: 1px;
    border-style: solid;
}
/* Links */
.ui-page-theme-d a,
html .ui-bar-d a,
html .ui-body-d a,
html body .ui-group-theme-d a {
    color: #3388cc /*{d-link-color}*/;
    font-weight: bold;
}
.ui-page-theme-d a:visited,
html .ui-bar-d a:visited,
html .ui-body-d a:visited,
html body .ui-group-theme-d a:visited {
    color: #3388cc /*{d-link-visited}*/;
}
.ui-page-theme-d a:hover,
html .ui-bar-d a:hover,
html .ui-body-d a:hover,
html body .ui-group-theme-d a:hover {
    color: #005599 /*{d-link-hover}*/;
}
.ui-page-theme-d a:active,
html .ui-bar-d a:active,
html .ui-body-d a:active,
html body .ui-group-theme-d a:active {
    color: #005599 /*{d-link-active}*/;
}
/* Button up */
.ui-page-theme-d .ui-btn,
html .ui-bar-d .ui-btn,
html .ui-body-d .ui-btn,
html body .ui-group-theme-d .ui-btn,
html head + body .ui-btn.ui-btn-d,
/* Button visited */
.ui-page-theme-d .ui-btn:visited,
html .ui-bar-d .ui-btn:visited,
html .ui-body-d .ui-btn:visited,
html body .ui-group-theme-d .ui-btn:visited,
html head + body .ui-btn.ui-btn-d:visited {
    background-color: #fcee21 /*{d-bup-background-color}*/;
    border-color: #8a8212 /*{d-bup-border}*/;
    color: #000000 /*{d-bup-color}*/;
    text-shadow: 0 /*{d-bup-shadow-x}*/ 1px /*{d-bup-shadow-y}*/ 0 /*{d-bup-shadow-radius}*/ #eeeeee /*{d-bup-shadow-color}*/;
}
/* Button hover */
.ui-page-theme-d .ui-btn:hover,
html .ui-bar-d .ui-btn:hover,
html .ui-body-d .ui-btn:hover,
html body .ui-group-theme-d .ui-btn:hover,
html head + body .ui-btn.ui-btn-d:hover {
    background-color: #FFFF25 /*{d-bhover-background-color}*/;
    border-color: #8c8c14 /*{d-bhover-border}*/;
    color: #000000 /*{d-bhover-color}*/;
    text-shadow: 0 /*{d-bhover-shadow-x}*/ 1px /*{d-bhover-shadow-y}*/ 0 /*{d-bhover-shadow-radius}*/ #eeeeee /*{d-bhover-shadow-color}*/;
}
/* Button down */
.ui-page-theme-d .ui-btn:active,
html .ui-bar-d .ui-btn:active,
html .ui-body-d .ui-btn:active,
html body .ui-group-theme-d .ui-btn:active,
html head + body .ui-btn.ui-btn-d:active {
    background-color: #FFFF25 /*{d-bdown-background-color}*/;
    border-color: #8c8c14 /*{d-bdown-border}*/;
    color: #000000 /*{d-bdown-color}*/;
    text-shadow: 0 /*{d-bdown-shadow-x}*/ 1px /*{d-bdown-shadow-y}*/ 0 /*{d-bdown-shadow-radius}*/ #eeeeee /*{d-bdown-shadow-color}*/;
}
/* Active button */
.ui-page-theme-d .ui-btn.ui-btn-active,
html .ui-bar-d .ui-btn.ui-btn-active,
html .ui-body-d .ui-btn.ui-btn-active,
html body .ui-group-theme-d .ui-btn.ui-btn-active,
html head + body .ui-btn.ui-btn-d.ui-btn-active,
/* Active checkbox icon */
.ui-page-theme-d .ui-checkbox-on:after,
html .ui-bar-d .ui-checkbox-on:after,
html .ui-body-d .ui-checkbox-on:after,
html body .ui-group-theme-d .ui-checkbox-on:after,
.ui-btn.ui-checkbox-on.ui-btn-d:after,
/* Active flipswitch background */
.ui-page-theme-d .ui-flipswitch-active,
html .ui-bar-d .ui-flipswitch-active,
html .ui-body-d .ui-flipswitch-active,
html body .ui-group-theme-d .ui-flipswitch-active,
html body .ui-flipswitch.ui-bar-d.ui-flipswitch-active,
/* Active slider track */
.ui-page-theme-d .ui-slider-track .ui-btn-active,
html .ui-bar-d .ui-slider-track .ui-btn-active,
html .ui-body-d .ui-slider-track .ui-btn-active,
html body .ui-group-theme-d .ui-slider-track .ui-btn-active,
html body div.ui-slider-track.ui-body-d .ui-btn-active {
    background-color: #3388cc /*{d-active-background-color}*/;
    border-color: #3388cc /*{d-active-border}*/;
    color: #ffffff /*{d-active-color}*/;
    text-shadow: 0 /*{d-active-shadow-x}*/ 1px /*{d-active-shadow-y}*/ 0 /*{d-active-shadow-radius}*/ #005599 /*{d-active-shadow-color}*/;
}
/* Active radio button icon */
.ui-page-theme-d .ui-radio-on:after,
html .ui-bar-d .ui-radio-on:after,
html .ui-body-d .ui-radio-on:after,
html body .ui-group-theme-d .ui-radio-on:after,
.ui-btn.ui-radio-on.ui-btn-d:after {
    border-color: #3388cc /*{d-active-background-color}*/;
}
/* Focus */
.ui-page-theme-d .ui-btn:focus,
html .ui-bar-d .ui-btn:focus,
html .ui-body-d .ui-btn:focus,
html body .ui-group-theme-d .ui-btn:focus,
html head + body .ui-btn.ui-btn-d:focus,
/* Focus buttons and text inputs with div wrap */
.ui-page-theme-d .ui-focus,
html .ui-bar-d .ui-focus,
html .ui-body-d .ui-focus,
html body .ui-group-theme-d .ui-focus,
html head + body .ui-btn-d.ui-focus,
html head + body .ui-body-d.ui-focus {
    -webkit-box-shadow: 0 0 12px #3388cc /*{d-active-background-color}*/;
    -moz-box-shadow: 0 0 12px #3388cc /*{d-active-background-color}*/;
    box-shadow: 0 0 12px #3388cc /*{d-active-background-color}*/;
}


/* Structure */
/* Disabled
-----------------------------------------------------------------------------------------------------------*/
/* Class ui-disabled deprecated in 1.4. :disabled not supported by IE8 so we use [disabled] */
.ui-disabled,
.ui-state-disabled,
button[disabled],
.ui-select .ui-btn.ui-state-disabled {
    filter: Alpha(Opacity=30);
    opacity: .3;
    cursor: default !important;
    pointer-events: none;
}
/* Focus state outline
-----------------------------------------------------------------------------------------------------------*/
.ui-btn:focus,
.ui-btn.ui-focus {
    outline: 0;
}
/* Unset box-shadow in browsers that don't do it right */
.ui-noboxshadow .ui-shadow,
.ui-noboxshadow .ui-shadow-inset,
.ui-noboxshadow .ui-overlay-shadow,
.ui-noboxshadow .ui-shadow-icon.ui-btn:after,
.ui-noboxshadow .ui-shadow-icon .ui-btn:after,
.ui-noboxshadow .ui-focus,
.ui-noboxshadow .ui-btn:focus,
.ui-noboxshadow  input:focus,
.ui-noboxshadow .ui-panel {
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
}
.ui-noboxshadow .ui-btn:focus,
.ui-noboxshadow .ui-focus {
    outline-width: 1px;
    outline-style: auto;
}

#mobileConsole{
    position: fixed;
    width: 100%;
    height: 200px;
    bottom: 300px;
    left: 0;
    z-index: 1000;
    display: none;
}

.task-category-btn {
    width:250px !important;
    height:230px !important;
    margin: auto !important;
    margin-top:8px !important;
}

.task-category-btn > i {
    font-size: 90px;
}

@media (max-width: 370px) { 
    .app-banner{
        width: 90%;
    }

    #plotInfo-popup{
        left: 0px !important;
    }

    #plotInfo{
        width: 90% !important;
    }

    .task-category-btn {
        width:12em !important;
        height:11em !important;
        margin: auto !important;
        margin-top:8px !important;
    }
    .task-category-btn > i {
        font-size: 60px;
    }

}

@media (max-height: 690px) {
    .task-category-btn {
        width:12em !important;
        height:11em !important;
        margin: auto !important;
        margin-top:8px !important;
    }
    .task-category-btn > i {
        font-size: 60px;
    }
}

@media (max-height: 585px) {
    .task-category-btn {
        width:10em !important;
        height:10em !important;
        margin: auto !important;
        margin-top:8px !important;
    }
    .task-category-btn > i {
        font-size: 60px;
    }
}

@media (max-height: 525px) {
    .task-category-btn {
        width:9em !important;
        height:9em !important;
        margin: auto !important;
        margin-top:3px !important;
    }
    .task-category-btn > i {
        font-size: 30px;
    }

    .task-category-btn > p {
        font-size: 17px !important;
    }
}
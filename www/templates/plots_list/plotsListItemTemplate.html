<li class="<%= row.styleClass %>">
	<a href="#map/task_id/<%= row.task_id %>/plot_id/<%= row.plot_gid %>">
		<span style="white-space:normal;"><%= row.plot_name %></span>
		<% if(row.demo_sampling)  { %>
			<p style="white-space:normal; font-weight: bold"><%= Localization.forDemoSampling %></p>
		<% } %>
		<p style="white-space:normal;"><%= Localization.sample_depth %>: <%= row.treatment_type_label %></p>
		<% if(row.leaf_sample_cells) { %>
			<p style="white-space:normal;"><%= Localization.treatment_types[2].name +': ' + row.leaf_sample_cells %></p>
		<% } %>
		<% if(row.cells_for_sampling) { %>
			<p style="white-space:normal;"><%= Localization.cells %>: <%= row.cells_for_sampling %></p>
		<% } %>
		<% if(row.ekatte) { %>
			<p style="white-space:normal;"><%= Localization.ekatte %>: <%= row.ekatte %></p> 
		<% } %>
		<% if(row.plot_area) { %>
			<p style="white-space:normal;"><%= Localization.plotArea %>: <%= row.plot_area + ' ' + Localization[row.area_unit] %></p>
		<% } %>
		<% if(row.date_assigned) { %>
			<p style="white-space:normal;"><%= Localization.dateAssigned %>: <%= row.date_assigned %></p>
		<% } %>
	</a>
</li>
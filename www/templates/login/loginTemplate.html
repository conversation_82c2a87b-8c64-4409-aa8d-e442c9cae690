<!-- Content -->
<div role="main" class="ui-content red-line" data-theme="a" style="margin-top: 50px;">
	<div class="text-center"><img class="app-banner" src="themes/images/agroballance.png" /></div>
	<h1 class="text-center"><%= Localization.unit_tracking %></h1>
	<input name="username" id="uname" value="" placeholder="<%= Localization.username %>" type="text">
	<input name="password" id="pass" value="" placeholder="<%= Localization.password %>" type="password">
	<a href="javascript:void(0)" class="ui-shadow ui-btn ui-btn-b ui-corner-all" id="login_button"><%= Localization.login %></a>
    
    <br>
    <fieldset id="change-locale" data-role="controlgroup" data-type="horizontal" align="center">
        <% _.each(languages, function(language) { %>
            <input type="radio" name="locale" id="locale-<%=language%>" value="<%=language%>" <% if (locale == language) { %> checked="checked" <% } %>>
            <label for="locale-<%=language%>"><%=Localization[language + '-short']%></label>
        <% }); %>
    </fieldset>
</div>
    
<div data-role="popup" id="msg_popup" data-overlay-theme="b" data-theme="b" data-dismissible="false">
	<div data-role="header" data-theme="a">
		<h1><%= Localization.error%>!</h1>
	</div>
	<div role="main" class="ui-content">
		<h3 class="ui-title" id="msg_content"></h3>
		<a href="javascript:void(0)" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-transition="flow">OK</a>
	</div>
</div>    
<!-- end Content -->
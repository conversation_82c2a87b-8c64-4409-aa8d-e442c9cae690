<style>
    #map { width:<%=viewport.width%>px; height:<%=viewport.height%>px; }

    #pins-slick {
        width: 350px;
        max-width: 350px;
    }

    #add-pin {
        display: none;
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 1005;
        background-color: #fff;
        top: 0;
        left: 0;
    }
</style>

<div id="popupChartImage" data-role="popup" data-overlay-theme="b" data-tolerance="10">
    <a href="javascript:void(0)" data-rel="back" class="ui-btn ui-corner-all ui-shadow ui-btn-a ui-icon-delete ui-btn-icon-notext ui-btn-right"></a>
    <img id="chart-image">
</div>

<!-- Content -->
<div id="map" class="map"></div>

<div id="watchPositionInfo">
    <button id="toggleGPSMeasure" data-action="start" class="ui-btn ui-corner-all ui-btn-b ui-mini"><%= Localization.start %></button>
    <div id="watchPositionText"></div>
</div>

<div id="compass-container">
    <div id="direction-heading-degrees"></div>
    <div id="direction-heading-distance"></div>
    <span id="arrow"></span>
</div>
<div id="plot-history-tabs" class="slick" style="display: block;">
    <div data-role="navbar" style="padding:5px;">
        <button id="start-sample-taking" data-action="start" class="ui-btn ui-corner-all ui-btn-b"><%= Localization.start %></button>
        <div id="workflow-list" class="ui-grid-a" style="display:none;" >
            <div class="ui-block-a" style="padding:0px 5px;"><button id="toggle-sample-taking" class="ui-btn ui-shadow ui-btn-d ui-corner-all"><%= Localization.stop %></button></div>
            <div class="ui-block-b" style="padding:0px 5px;"><button id="end-sample-taking" data-action="pause" class="ui-shadow ui-btn ui-btn-b ui-corner-all""><%= Localization.pause %></button></div>
        </div>
    </div>
</div>
<div id="add-pin"></div>

<div id="workflow-popup-container"></div>

<div id="add-leaf-pin-popup-container"></div>

<div data-role="popup" id="plotInfo" class="ui-content" style="width:360px;height:260px;padding:10px;" data-dismissible="false">
    <a href="#" data-rel="back" class="ui-btn ui-corner-all ui-shadow ui-icon-delete ui-btn-icon-notext ui-btn-right">Close</a>
    <div data-role="header">
        <h3 style="margin:auto;"><%= Localization.information %></h3>
    </div>
    <div class="ui-body">
        <div class="ui-grid-a">
            <div class="ui-block-a" style="height:160px;padding-top:12px !important;">
                <ul data-role="listview">
                    <li><%= Localization.client_name %> </li>
                    <li><%= Localization.plot_name %></li>
                    <li><%= Localization.ekatte %></li>
                    <li><%= Localization.sample_type %></li>
                </ul>
            </div>
            <div class="ui-block-b" style="padding:auto;padding-top:12px !important;">
                <ul data-role="listview">
                </ul>
            </div>
        </div>
        <a id="google-maps-route-btn" href="#" class="ui-btn ui-btn-b ui-corner-all ui-shadow" style="clear:both;"><%= Localization.road_to_plot %></a>
    </div>
</div>

<div data-role="popup" id="endSampleTakingPopup" data-overlay-theme="b" data-theme="a" data-dismissible="false">
    <div data-role="header" data-theme="a">
        <h3 style="margin:auto"><%= Localization.end_sampling %></h3>
    </div>
    <div class="ui-body">
        <label for="radio-choice-unfinished"> <%= Localization.unfinished_sampling %>
            <input type="radio" name="radio-choice" id="radio-choice-unfinished">
        </label>
        <label for="radio-choice-finished"><%= Localization.finished_sampling %>
            <input type="radio" name="radio-choice" id="radio-choice-finished">
        </label>
    </div>
    <div role="main" class="ui-content">
        <button id="popup-end-sample-taking" class="ui-btn ui-shadow ui-corner-all ui-btn-b"><%= Localization.ok %></button>
    </div>
</div>
<!-- end Content -->


<div id="mobileConsole">
<form>
    <label for="consoleOutput">Textarea:</label>
    <textarea name="consoleOutput" id="consoleOutput" style="height:300px;"></textarea>
</form>
</div>




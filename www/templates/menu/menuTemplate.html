<!-- Panel - Main menu -->
<div data-role="panel" data-display="overlay" data-position="left" data-position-fixed="true" data-theme="a" id="nav-panel">
    <ul data-role="listview">
        <li><span class="filler"></span></li>
        <li><a href="#tasks"><%= Localization.tasks %></a></li>
        <li><a href="#finishedTasks"><%= Localization.finished_tasks %></a></li>
        <li><a href="#contacts" id="contacts"><%= Localization.contacts %></a></li>
        <li><a href="#logout"><%= exit_text %></a></li>
    </ul>
</div>
<!-- end Panel - Main menu -->

<!-- Panel - Options -->
<div data-role="panel" data-display="overlay" data-theme="a" data-position="right" data-position-fixed="true" id="nav-settings">
    <fieldset data-role="controlgroup">
        <legend><%=Localization.language%>:</legend>
        <div class="ui-controlgroup-controls" id="change-locale">
            <% _.each(languages, function(language) { %>
                <input <% if (locale == language) { %> checked="checked" <% } %>  name="locale" id="locale-<%=language%>" value="<%=language%>" type="radio">
                <label for="locale-<%=language%>"><%=Localization[language]%></label>
            <% }); %>
        </div>
    </fieldset>

    <fieldset data-role="controlgroup" class="<% if(page_name != 'map' && page_name != 'plotOnMap' && page_name != 'pinOnMap') { %>ui-screen-hidden<% } %>">
        <legend><%=Localization.maptype%>:</legend>
        <div class="ui-controlgroup-controls" id="change-map-type">
            <input <% if (map_type.code == 'google') { %> checked="checked" <% } %> name="map-type" id="map-google" value="google" type="radio">
                <label for="map-google">Google</label>
            <input <% if (map_type.code == 'bing') { %> checked="checked" <% } %> name="map-type" id="map-bing" value="bing" type="radio">
                <label for="map-bing">Bing</label>
            <input <% if (map_type.code == 'geoscan') { %> checked="checked" <% } %> name="map-type" id="map-geoscan" value="geoscan" type="radio">
                <label for="map-geoscan">GeoSCAN</label>
            <input <% if (map_type.code == 'none') { %> checked="checked" <% } %> name="map-type" id="map-none" value="none" type="radio">
                <label for="map-none"><%= Localization.no_map_pad %></label>
            <div id="gs-by-date-div" style="display: none;">
                <input id="gs-by-date" type="text" placeholder="<%=Localization.choose_date%>" readonly>
            </div>
        </div>
    </fieldset>

    <fieldset data-role="controlgroup" class="<% if(page_name != 'map' && page_name != 'plotOnMap' && page_name != 'pinOnMap' && page_name != 'task_plots') { %>ui-screen-hidden<% } %>">
        <legend><%=Localization.useExternalAntenna%>:</legend>
        <div class="ui-controlgroup-controls" id="location-provider">
            <input checked="checked" name="location-provider-type" id="external" value="on" type="radio">
                <label for="external"><%= Localization.on %></label>
            <input name="location-provider-type" id="internal" value="off" type="radio">
                <label for="internal"><%= Localization.off %></label>
        </div>
    </fieldset>

    <fieldset class="<% if(page_name != 'map' && page_name != 'plotOnMap' && page_name != 'pinOnMap' && page_name != 'task_plots') { %>ui-screen-hidden<% } %>">
        <legend><%= Localization.show_position %></legend>
        <label>
                <input type="checkbox" name="checkbox-zoom" id="checkbox-zoom"><%= Localization.zoom %>
        </label>
        <label>
                <input type="checkbox" name="checkbox-pan" id="checkbox-pan"><%= Localization.pan %>
        </label>
    </fieldset>

    <fieldset class="">
        <legend></legend>
        <button id="exportData" class="ui-shadow ui-btn ui-corner-all"><i class="fa fa-save fa-3x"></i></button>
    </fieldset>

</div>
<!-- end Panel - Options -->

<div id="header" data-role="header" data-theme="a" data-position="fixed" data-tap-toggle="false" class="red-line ui-header ui-bar-a ui-header-fixed slidedown" role="banner">
    <h1 class="ui-title menu-title" role="heading" aria-level="1"><% Localization.map %></h1>
    <a href="#nav-panel" class="header-menu-btn ui-link ui-btn-left ui-btn ui-shadow ui-corner-all"><span class="fi icon-dot-menu"></span><span class="menu-logo"></span></a>
    <a href="#nav-settings" class="header-menu-btn ui-link ui-btn-right ui-btn ui-shadow ui-corner-all"><span class="fi icon-cog"></span> </a>
</div>

<div data-role="popup" id="msg_popup" data-overlay-theme="b" data-theme="b" data-dismissible="false">
    <div data-role="header" data-theme="a">
        <h1><%= Localization.error%>!</h1>
    </div>
    <div role="main" class="ui-content">
        <h3 class="ui-title" id="msg_content"></h3>
        <a href="javascript:void(0)" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-transition="flow">OK</a>
    </div>
</div>

<div data-role="popup" id="loading-popup" data-overlay-theme="b" data-dismissible="false"></div>
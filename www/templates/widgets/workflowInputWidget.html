<div data-role="popup" id="popup-workflow-stop" data-dismissible="false">
    <form>
        <div data-role="tabs">
            <div data-role="navbar">
                <ul id="tabs">
                    <% for (var i = 0; i < treatment_types.length; i++) { %>
                        <li id="treatment-tab-index-<%= treatment_types[i].value %>"><a href="<%= '#treatment-tab-' + treatment_types[i].value.toString() %>" <% if(defaultActiveTab == treatment_types[i].value){%>class="ui-btn ui-btn-active" <%}%>><%= treatment_types[i].short_name %></a></li>
                    <% } %>
                </ul>
            </div>
            <% for (var i = 0; i < treatment_types.length; i++) { %>
                <div id="treatment-tab-<%= treatment_types[i].value %>" class="ui-corner-all custom-corners">
                    <div class="ui-body ui-body-a">
                        <label for="sample-number-<%= treatment_types[i].value %>"><%= Localization.set_number %> <span class="req">*</span></label>
                        <div class="ui-block-a"><a data-treatment="<%= treatment_types[i].value.toString() %>" class="ui-btn ui-corner-all ui-mini ui-btn-icon scan-barcode-btn" ><i class="fa fa-barcode fa-2x"></i></a></div>
                        <div class="ui-block-b"><input type="number" id="sample-number-<%= treatment_types[i].value %>" value="" placeholder="<%= Localization.sample_number %>"></input></div>
                    </div>
                </div>
            <% }%>
        </div>
        <div data-role="navbar">
            <ul>
                <li><button id="popup-workflow-stop-cancel" class="ui-btn ui-corner-all"><%= Localization.cancel %></button></li>
                <li><button id="popup-workflow-stop-ok" class="ui-btn ui-corner-all"><%= Localization.ok %></button></li>
            </ul>
        </div>
    </form>
</div>
{"name": "com.agrobalance", "displayName": "SoilSamples", "version": "2.0.0", "description": "Soil Samples App", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ecosystem:cordova"], "author": "Apache Cordova Team", "license": "Apache-2.0", "devDependencies": {"cordova-android": "^14.0.1", "cordova-plugin-appinfo": "^2.1.2", "cordova-plugin-bluetooth-serial": "^0.4.7", "cordova-plugin-camera": "^8.0.0", "cordova-plugin-device": "^3.0.0", "cordova-plugin-device-orientation": "^3.0.0", "cordova-plugin-dialogs": "^2.0.2", "cordova-plugin-file": "^8.1.3", "cordova-plugin-file-transfer": "^2.0.0", "cordova-plugin-geolocation": "^5.0.0", "cordova-plugin-zip": "^3.1.0", "phonegap-plugin-barcodescanner": "^8.1.0"}, "cordova": {"plugins": {"cordova-plugin-zip": {}, "cordova-plugin-appinfo": {}, "cordova-plugin-camera": {}, "cordova-plugin-device": {}, "cordova-plugin-dialogs": {}, "cordova-plugin-file": {}, "cordova-plugin-file-transfer": {}, "cordova-plugin-geolocation": {}, "cordova-plugin-device-orientation": {}, "phonegap-plugin-barcodescanner": {}, "cordova-plugin-bluetooth-serial": {}}, "platforms": ["android"]}}